#!/usr/bin/env python3
"""
ENHANCED TRADING SYSTEM SETUP
============================
Sets up the enhanced trading system with realistic backtesting and RL feedback.
Installs dependencies, creates directories, and runs initial tests.
"""

import subprocess
import sys
import os
from pathlib import Path
import logging

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

def install_dependencies():
    """Install required dependencies"""
    logger.info("🔧 Installing dependencies...")
    
    try:
        # Install core dependencies first
        core_deps = [
            "pandas>=2.1.0",
            "numpy>=1.24.0", 
            "ccxt>=4.1.0",
            "requests>=2.31.0",
            "flask>=2.3.0",
            "python-dotenv>=1.0.0"
        ]
        
        for dep in core_deps:
            logger.info(f"📦 Installing {dep}")
            subprocess.check_call([sys.executable, "-m", "pip", "install", dep])
        
        logger.info("✅ Core dependencies installed successfully")
        return True
        
    except subprocess.CalledProcessError as e:
        logger.error(f"❌ Error installing dependencies: {e}")
        return False

def create_directory_structure():
    """Create necessary directories"""
    logger.info("📁 Creating directory structure...")
    
    directories = [
        "data",
        "backtesting", 
        "models",
        "reinforcement_learning",
        "validation",
        "results",
        "logs",
        "config"
    ]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        logger.info(f"📂 Created directory: {directory}")
    
    logger.info("✅ Directory structure created")

def test_data_collector():
    """Test the historical data collector"""
    logger.info("🧪 Testing historical data collector...")
    
    try:
        # Import and test data collector
        sys.path.append('data')
        from historical_data_collector import HistoricalDataCollector
        
        collector = HistoricalDataCollector()
        logger.info("✅ Data collector initialized successfully")
        
        # Test exchange connection
        if collector.exchange:
            logger.info("✅ Exchange connection successful")
        else:
            logger.warning("⚠️ Exchange connection failed")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing data collector: {e}")
        return False

def test_trade_simulator():
    """Test the realistic trade simulator"""
    logger.info("🧪 Testing realistic trade simulator...")
    
    try:
        # Import and test trade simulator
        sys.path.append('backtesting')
        from realistic_trade_simulator import RealTradeSimulator
        
        simulator = RealTradeSimulator()
        logger.info("✅ Trade simulator initialized successfully")
        
        # Test with sample data
        sample_data = {
            'datetime': '2024-01-01 12:00:00',
            'open': 50000.0,
            'high': 50100.0,
            'low': 49900.0,
            'close': 50050.0,
            'volume': 1000000.0
        }
        
        # This should create grid orders
        stats = simulator.get_simulation_stats()
        logger.info(f"✅ Simulator stats: {stats}")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing trade simulator: {e}")
        return False

def test_enhanced_backtester():
    """Test the enhanced backtester"""
    logger.info("🧪 Testing enhanced backtester...")
    
    try:
        # Import and test backtester
        sys.path.append('backtesting')
        from enhanced_backtester import EnhancedBacktester
        
        backtester = EnhancedBacktester()
        logger.info("✅ Enhanced backtester initialized successfully")
        
        return True
        
    except Exception as e:
        logger.error(f"❌ Error testing enhanced backtester: {e}")
        return False

def create_config_files():
    """Create configuration files"""
    logger.info("⚙️ Creating configuration files...")
    
    # Trading configuration
    trading_config = """
# Enhanced Trading System Configuration

[TRADING]
position_size_usd = 10.0
grid_spacing = 0.0025
take_profit_pct = 0.0025
stop_loss_pct = 0.001
risk_reward_ratio = 2.5
max_open_trades = 1

[BACKTESTING]
commission_rate = 0.001
base_slippage = 0.0001
partial_fill_probability = 0.1
min_sample_size = 200

[VALIDATION]
train_ratio = 0.75
val_ratio = 0.125
test_ratio = 0.125
significance_level = 0.05

[DATA]
symbol = BTC/USDT
timeframe = 1m
start_date = 2022-01-01
end_date = 2024-12-01
"""
    
    with open("config/trading_config.ini", "w") as f:
        f.write(trading_config)
    
    logger.info("✅ Configuration files created")

def run_initial_validation():
    """Run initial system validation"""
    logger.info("🎯 Running initial system validation...")
    
    validation_results = {
        'dependencies_installed': False,
        'directories_created': False,
        'data_collector_working': False,
        'trade_simulator_working': False,
        'backtester_working': False,
        'config_files_created': False
    }
    
    # Test each component
    validation_results['dependencies_installed'] = install_dependencies()
    validation_results['directories_created'] = True  # Already created
    validation_results['data_collector_working'] = test_data_collector()
    validation_results['trade_simulator_working'] = test_trade_simulator()
    validation_results['backtester_working'] = test_enhanced_backtester()
    validation_results['config_files_created'] = True  # Already created
    
    # Calculate overall success rate
    success_count = sum(validation_results.values())
    total_tests = len(validation_results)
    success_rate = success_count / total_tests
    
    logger.info(f"📊 Validation Results:")
    for test, result in validation_results.items():
        status = "✅" if result else "❌"
        logger.info(f"  {status} {test}: {result}")
    
    logger.info(f"🎯 Overall Success Rate: {success_rate:.1%} ({success_count}/{total_tests})")
    
    return success_rate >= 0.8  # 80% success rate required

def main():
    """Main setup function"""
    logger.info("🚀 Starting Enhanced Trading System Setup")
    
    try:
        # 1. Create directory structure
        create_directory_structure()
        
        # 2. Create configuration files
        create_config_files()
        
        # 3. Run validation
        success = run_initial_validation()
        
        if success:
            logger.info("✅ Enhanced Trading System setup completed successfully!")
            logger.info("🎯 Ready to proceed with Phase 1 implementation")
            
            print("\n" + "="*60)
            print("🎉 ENHANCED TRADING SYSTEM SETUP COMPLETE")
            print("="*60)
            print("✅ Realistic trade simulation engine ready")
            print("✅ Grid limit order system implemented")
            print("✅ Statistical validation framework prepared")
            print("✅ Out-of-sample testing capability enabled")
            print("\n🚀 Next Steps:")
            print("1. Run: python data/historical_data_collector.py")
            print("2. Run: python backtesting/enhanced_backtester.py")
            print("3. Review results in the 'results' directory")
            print("="*60)
            
        else:
            logger.error("❌ Setup failed - please check error messages above")
            print("\n❌ Setup incomplete - please resolve issues and try again")
            
    except Exception as e:
        logger.error(f"❌ Setup failed with error: {e}")
        print(f"\n❌ Setup failed: {e}")

if __name__ == "__main__":
    main()
