#!/usr/bin/env python3
"""
DEBUG RL ISSUE - Find the source of the low >= high error
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# Add paths for imports
sys.path.append('reinforcement_learning')
sys.path.append('backtesting')

def create_sample_data(num_points: int, base_price: float = 50000) -> pd.DataFrame:
    """Create sample OHLCV data with proper relationships"""
    dates = pd.date_range(start='2024-01-01', periods=num_points, freq='1min')
    
    # Generate price series with random walk
    price_changes = np.cumsum(np.random.randn(num_points) * 10)  # Random walk
    prices = base_price + price_changes
    
    # Generate OHLC with proper relationships
    opens = prices + np.random.randn(num_points) * 5
    closes = prices + np.random.randn(num_points) * 5
    
    # Ensure high >= max(open, close) and low <= min(open, close)
    max_oc = np.maximum(opens, closes)
    min_oc = np.minimum(opens, closes)
    
    highs = max_oc + np.abs(np.random.randn(num_points)) * 20
    lows = min_oc - np.abs(np.random.randn(num_points)) * 20
    
    volumes = 1000000 + np.abs(np.random.randn(num_points)) * 200000
    
    # Validate OHLC relationships
    assert np.all(highs >= opens), "High must be >= Open"
    assert np.all(highs >= closes), "High must be >= Close"
    assert np.all(lows <= opens), "Low must be <= Open"
    assert np.all(lows <= closes), "Low must be <= Close"
    assert np.all(highs >= lows), "High must be >= Low"
    
    return pd.DataFrame({
        'datetime': dates,
        'open': opens,
        'high': highs,
        'low': lows,
        'close': closes,
        'volume': volumes
    })

def test_trading_environment_isolated():
    """Test trading environment in isolation"""
    print("🧪 Testing Trading Environment (Isolated)...")
    
    try:
        from trading_environment import TradingEnvironment
        
        # Create and validate sample data
        sample_data = create_sample_data(100)
        print(f"✅ Sample data created: {len(sample_data)} records")
        
        # Validate data before passing to environment
        print("📊 Data validation:")
        print(f"  High >= Open: {np.all(sample_data['high'] >= sample_data['open'])}")
        print(f"  High >= Close: {np.all(sample_data['high'] >= sample_data['close'])}")
        print(f"  Low <= Open: {np.all(sample_data['low'] <= sample_data['open'])}")
        print(f"  Low <= Close: {np.all(sample_data['low'] <= sample_data['close'])}")
        print(f"  High >= Low: {np.all(sample_data['high'] >= sample_data['low'])}")
        
        # Check for any problematic values
        problematic_rows = sample_data[sample_data['high'] < sample_data['low']]
        if len(problematic_rows) > 0:
            print(f"❌ Found {len(problematic_rows)} problematic rows:")
            print(problematic_rows)
            return False
        
        # Create environment
        print("🏗️ Creating trading environment...")
        env = TradingEnvironment(sample_data, max_episode_steps=20)
        print("✅ Environment created successfully")
        
        # Test reset
        print("🔄 Testing environment reset...")
        obs = env.reset()
        print(f"✅ Environment reset - Observation shape: {obs.shape}")
        
        # Test a few steps
        print("👟 Testing environment steps...")
        for step in range(3):
            action = np.random.randint(0, env.action_space_size)
            obs, reward, done, info = env.step(action)
            print(f"  Step {step}: Action={action}, Reward={reward:.4f}, Done={done}")
            
            if done:
                break
        
        print("✅ Trading environment test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Trading environment test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_rl_feedback_loop_isolated():
    """Test RL feedback loop in isolation"""
    print("\n🧪 Testing RL Feedback Loop (Isolated)...")
    
    try:
        from rl_feedback_loop import RLFeedbackLoop
        
        # Create and validate sample data
        sample_data = create_sample_data(200)
        print(f"✅ Sample data created: {len(sample_data)} records")
        
        # Validate data
        print("📊 Data validation:")
        print(f"  High >= Low: {np.all(sample_data['high'] >= sample_data['low'])}")
        
        # Check for any problematic values
        problematic_rows = sample_data[sample_data['high'] < sample_data['low']]
        if len(problematic_rows) > 0:
            print(f"❌ Found {len(problematic_rows)} problematic rows:")
            print(problematic_rows)
            return False
        
        # Create feedback loop
        print("🔄 Creating RL feedback loop...")
        feedback_loop = RLFeedbackLoop(
            historical_data=sample_data,
            validation_frequency=10,
            max_training_episodes=5
        )
        print("✅ Feedback loop created successfully")
        
        # Test single training episode
        print("🎯 Testing single training episode...")
        episode_metrics = feedback_loop.run_training_episode(1)
        print(f"✅ Training episode completed:")
        print(f"  Episode reward: {episode_metrics['episode_reward']:.4f}")
        print(f"  Portfolio return: {episode_metrics['portfolio_return']:.2f}%")
        
        print("✅ RL feedback loop test completed successfully")
        return True
        
    except Exception as e:
        print(f"❌ RL feedback loop test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main debug function"""
    print("🔍 DEBUGGING RL SYSTEM ISSUES")
    print("="*50)
    
    # Test data generation
    print("📊 Testing data generation...")
    try:
        data = create_sample_data(50)
        print(f"✅ Data generation successful: {len(data)} records")
    except Exception as e:
        print(f"❌ Data generation failed: {e}")
        return
    
    # Test components individually
    env_success = test_trading_environment_isolated()
    feedback_success = test_rl_feedback_loop_isolated()
    
    print("\n📊 DEBUG RESULTS:")
    print("="*50)
    print(f"{'✅' if env_success else '❌'} Trading Environment")
    print(f"{'✅' if feedback_success else '❌'} RL Feedback Loop")
    
    if env_success and feedback_success:
        print("\n🎉 All components working individually!")
        print("The issue might be in the integration or test setup.")
    else:
        print("\n⚠️ Found issues in individual components.")

if __name__ == "__main__":
    main()
