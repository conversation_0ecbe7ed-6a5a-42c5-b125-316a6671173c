#!/usr/bin/env python3
"""
RISK ANALYZER - COMPREHENSIVE RISK ASSESSMENT
============================================
Implements comprehensive risk analysis including VaR, stress testing,
and risk-adjusted performance metrics for trading strategies.
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.optimize import minimize
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class RiskAnalyzer:
    """
    Comprehensive risk analysis for trading strategies
    """
    
    def __init__(self, confidence_levels: List[float] = [0.95, 0.99]):
        self.confidence_levels = confidence_levels
        
        # Risk analysis configuration
        self.config = {
            'var_methods': ['historical', 'parametric', 'monte_carlo'],
            'stress_scenarios': ['market_crash', 'volatility_spike', 'trend_reversal'],
            'monte_carlo_simulations': 10000,
            'bootstrap_iterations': 1000,
            'rolling_window_days': 30,
            'risk_free_rate': 0.02  # 2% annual risk-free rate
        }
        
        logger.info(f"⚠️ Risk Analyzer initialized")
        logger.info(f"📊 Confidence levels: {confidence_levels}")
        logger.info(f"🎲 Monte Carlo simulations: {self.config['monte_carlo_simulations']}")
    
    def analyze_strategy_risk(self, returns: List[float], 
                             prices: Optional[List[float]] = None,
                             benchmark_returns: Optional[List[float]] = None) -> Dict:
        """
        Comprehensive risk analysis of trading strategy
        
        Args:
            returns: List of strategy returns (as percentages)
            prices: Optional list of underlying asset prices
            benchmark_returns: Optional benchmark returns for comparison
            
        Returns:
            Dictionary with comprehensive risk metrics
        """
        
        logger.info(f"⚠️ Analyzing risk for {len(returns)} return observations")
        
        # Convert to numpy arrays
        returns_array = np.array(returns) / 100  # Convert percentages to decimals
        
        # Basic risk metrics
        basic_metrics = self._calculate_basic_risk_metrics(returns_array)
        
        # Value at Risk (VaR) analysis
        var_analysis = self._calculate_var(returns_array)
        
        # Expected Shortfall (Conditional VaR)
        es_analysis = self._calculate_expected_shortfall(returns_array)
        
        # Drawdown analysis
        drawdown_analysis = self._analyze_drawdowns(returns_array)
        
        # Risk-adjusted performance
        risk_adjusted_metrics = self._calculate_risk_adjusted_metrics(returns_array, benchmark_returns)
        
        # Stress testing
        stress_test_results = self._perform_stress_tests(returns_array)
        
        # Tail risk analysis
        tail_risk = self._analyze_tail_risk(returns_array)
        
        # Rolling risk metrics
        rolling_metrics = self._calculate_rolling_risk_metrics(returns_array)
        
        # Risk attribution (if prices provided)
        risk_attribution = self._analyze_risk_attribution(returns_array, prices)
        
        # Overall risk assessment
        risk_assessment = self._assess_overall_risk(
            basic_metrics, var_analysis, drawdown_analysis, tail_risk
        )
        
        return {
            'analysis_type': 'comprehensive_risk',
            'sample_size': len(returns),
            'analysis_period_days': len(returns),  # Assuming daily returns
            'basic_metrics': basic_metrics,
            'var_analysis': var_analysis,
            'expected_shortfall': es_analysis,
            'drawdown_analysis': drawdown_analysis,
            'risk_adjusted_performance': risk_adjusted_metrics,
            'stress_test_results': stress_test_results,
            'tail_risk_analysis': tail_risk,
            'rolling_metrics': rolling_metrics,
            'risk_attribution': risk_attribution,
            'overall_assessment': risk_assessment,
            'timestamp': datetime.now().isoformat()
        }
    
    def _calculate_basic_risk_metrics(self, returns: np.ndarray) -> Dict:
        """Calculate basic risk metrics"""
        
        # Annualized metrics (assuming daily returns)
        annual_factor = np.sqrt(252)  # 252 trading days per year
        
        return {
            'volatility': {
                'daily': float(np.std(returns, ddof=1)),
                'annualized': float(np.std(returns, ddof=1) * annual_factor)
            },
            'mean_return': {
                'daily': float(np.mean(returns)),
                'annualized': float(np.mean(returns) * 252)
            },
            'skewness': float(stats.skew(returns)),
            'kurtosis': float(stats.kurtosis(returns)),
            'excess_kurtosis': float(stats.kurtosis(returns, fisher=True)),
            'jarque_bera_test': {
                'statistic': float(stats.jarque_bera(returns)[0]),
                'p_value': float(stats.jarque_bera(returns)[1]),
                'is_normal': bool(stats.jarque_bera(returns)[1] > 0.05)
            },
            'downside_deviation': float(np.std(returns[returns < 0], ddof=1)) if np.any(returns < 0) else 0.0,
            'upside_deviation': float(np.std(returns[returns > 0], ddof=1)) if np.any(returns > 0) else 0.0
        }
    
    def _calculate_var(self, returns: np.ndarray) -> Dict:
        """Calculate Value at Risk using multiple methods"""
        
        var_results = {}
        
        for confidence_level in self.confidence_levels:
            alpha = 1 - confidence_level
            
            # Historical VaR
            historical_var = np.percentile(returns, alpha * 100)
            
            # Parametric VaR (assuming normal distribution)
            mean_return = np.mean(returns)
            std_return = np.std(returns, ddof=1)
            parametric_var = mean_return + stats.norm.ppf(alpha) * std_return
            
            # Monte Carlo VaR
            mc_returns = np.random.normal(mean_return, std_return, self.config['monte_carlo_simulations'])
            monte_carlo_var = np.percentile(mc_returns, alpha * 100)
            
            var_results[f'confidence_{int(confidence_level*100)}'] = {
                'historical_var': float(historical_var),
                'parametric_var': float(parametric_var),
                'monte_carlo_var': float(monte_carlo_var),
                'var_comparison': {
                    'historical_vs_parametric': float(historical_var - parametric_var),
                    'historical_vs_monte_carlo': float(historical_var - monte_carlo_var)
                }
            }
        
        return var_results
    
    def _calculate_expected_shortfall(self, returns: np.ndarray) -> Dict:
        """Calculate Expected Shortfall (Conditional VaR)"""
        
        es_results = {}
        
        for confidence_level in self.confidence_levels:
            alpha = 1 - confidence_level
            
            # Historical Expected Shortfall
            var_threshold = np.percentile(returns, alpha * 100)
            tail_returns = returns[returns <= var_threshold]
            
            if len(tail_returns) > 0:
                historical_es = np.mean(tail_returns)
            else:
                historical_es = var_threshold
            
            # Parametric Expected Shortfall (assuming normal distribution)
            mean_return = np.mean(returns)
            std_return = np.std(returns, ddof=1)
            
            # For normal distribution: ES = μ + σ * φ(Φ^(-1)(α)) / α
            z_alpha = stats.norm.ppf(alpha)
            parametric_es = mean_return + std_return * stats.norm.pdf(z_alpha) / alpha
            
            es_results[f'confidence_{int(confidence_level*100)}'] = {
                'historical_es': float(historical_es),
                'parametric_es': float(parametric_es),
                'tail_observations': len(tail_returns),
                'es_var_ratio': float(historical_es / var_threshold) if var_threshold != 0 else 0
            }
        
        return es_results
    
    def _analyze_drawdowns(self, returns: np.ndarray) -> Dict:
        """Analyze drawdown characteristics"""
        
        # Calculate cumulative returns
        cumulative_returns = np.cumprod(1 + returns)
        
        # Calculate running maximum
        running_max = np.maximum.accumulate(cumulative_returns)
        
        # Calculate drawdowns
        drawdowns = (cumulative_returns - running_max) / running_max
        
        # Find drawdown periods
        drawdown_periods = []
        in_drawdown = False
        start_idx = 0
        
        for i, dd in enumerate(drawdowns):
            if dd < 0 and not in_drawdown:
                # Start of drawdown
                in_drawdown = True
                start_idx = i
            elif dd >= 0 and in_drawdown:
                # End of drawdown
                in_drawdown = False
                drawdown_periods.append({
                    'start': start_idx,
                    'end': i - 1,
                    'length': i - start_idx,
                    'max_drawdown': float(np.min(drawdowns[start_idx:i]))
                })
        
        # Handle case where we end in drawdown
        if in_drawdown:
            drawdown_periods.append({
                'start': start_idx,
                'end': len(drawdowns) - 1,
                'length': len(drawdowns) - start_idx,
                'max_drawdown': float(np.min(drawdowns[start_idx:]))
            })
        
        # Calculate statistics
        max_drawdown = float(np.min(drawdowns))
        avg_drawdown = float(np.mean([dd for dd in drawdowns if dd < 0])) if np.any(drawdowns < 0) else 0.0
        
        # Recovery analysis
        recovery_times = []
        for period in drawdown_periods:
            if period['end'] < len(drawdowns) - 1:  # Recovered
                recovery_times.append(period['length'])
        
        return {
            'max_drawdown': max_drawdown,
            'max_drawdown_pct': max_drawdown * 100,
            'average_drawdown': avg_drawdown,
            'number_of_drawdowns': len(drawdown_periods),
            'average_drawdown_length': float(np.mean([p['length'] for p in drawdown_periods])) if drawdown_periods else 0,
            'max_drawdown_length': max([p['length'] for p in drawdown_periods]) if drawdown_periods else 0,
            'average_recovery_time': float(np.mean(recovery_times)) if recovery_times else None,
            'current_drawdown': float(drawdowns[-1]),
            'time_in_drawdown': float(np.mean(drawdowns < 0)),
            'drawdown_periods': drawdown_periods[:10]  # Limit to first 10 for brevity
        }
    
    def _calculate_risk_adjusted_metrics(self, returns: np.ndarray, 
                                       benchmark_returns: Optional[List[float]] = None) -> Dict:
        """Calculate risk-adjusted performance metrics"""
        
        annual_factor = 252
        risk_free_rate = self.config['risk_free_rate'] / annual_factor  # Daily risk-free rate
        
        # Basic metrics
        mean_return = np.mean(returns)
        std_return = np.std(returns, ddof=1)
        
        # Sharpe ratio
        sharpe_ratio = (mean_return - risk_free_rate) / std_return if std_return > 0 else 0
        
        # Sortino ratio (using downside deviation)
        downside_returns = returns[returns < risk_free_rate]
        downside_std = np.std(downside_returns, ddof=1) if len(downside_returns) > 1 else std_return
        sortino_ratio = (mean_return - risk_free_rate) / downside_std if downside_std > 0 else 0
        
        # Calmar ratio (return / max drawdown)
        cumulative_returns = np.cumprod(1 + returns)
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = (cumulative_returns - running_max) / running_max
        max_drawdown = abs(np.min(drawdowns))
        calmar_ratio = (mean_return * annual_factor) / max_drawdown if max_drawdown > 0 else 0
        
        metrics = {
            'sharpe_ratio': {
                'daily': float(sharpe_ratio),
                'annualized': float(sharpe_ratio * np.sqrt(annual_factor))
            },
            'sortino_ratio': {
                'daily': float(sortino_ratio),
                'annualized': float(sortino_ratio * np.sqrt(annual_factor))
            },
            'calmar_ratio': float(calmar_ratio),
            'information_ratio': None,  # Requires benchmark
            'treynor_ratio': None,      # Requires beta
            'jensen_alpha': None        # Requires benchmark
        }
        
        # Benchmark-relative metrics
        if benchmark_returns is not None:
            benchmark_array = np.array(benchmark_returns) / 100
            
            if len(benchmark_array) == len(returns):
                # Information ratio
                excess_returns = returns - benchmark_array
                tracking_error = np.std(excess_returns, ddof=1)
                information_ratio = np.mean(excess_returns) / tracking_error if tracking_error > 0 else 0
                
                # Beta and Treynor ratio
                covariance = np.cov(returns, benchmark_array)[0, 1]
                benchmark_variance = np.var(benchmark_array, ddof=1)
                beta = covariance / benchmark_variance if benchmark_variance > 0 else 0
                treynor_ratio = (mean_return - risk_free_rate) / beta if beta > 0 else 0
                
                # Jensen's Alpha
                benchmark_mean = np.mean(benchmark_array)
                jensen_alpha = mean_return - (risk_free_rate + beta * (benchmark_mean - risk_free_rate))
                
                metrics.update({
                    'information_ratio': float(information_ratio),
                    'treynor_ratio': float(treynor_ratio),
                    'jensen_alpha': float(jensen_alpha),
                    'beta': float(beta),
                    'tracking_error': float(tracking_error)
                })
        
        return metrics
    
    def _perform_stress_tests(self, returns: np.ndarray) -> Dict:
        """Perform stress testing scenarios"""
        
        stress_results = {}
        
        # Market crash scenario (-20% market drop)
        crash_returns = returns - 0.20  # Subtract 20% from all returns
        crash_var_95 = np.percentile(crash_returns, 5)
        
        # Volatility spike scenario (double volatility)
        current_vol = np.std(returns, ddof=1)
        high_vol_returns = np.random.normal(np.mean(returns), current_vol * 2, len(returns))
        vol_spike_var_95 = np.percentile(high_vol_returns, 5)
        
        # Trend reversal scenario
        trend_reversal_returns = -returns  # Reverse all returns
        reversal_var_95 = np.percentile(trend_reversal_returns, 5)
        
        stress_results = {
            'market_crash_20pct': {
                'scenario_description': '20% market crash applied to all returns',
                'var_95': float(crash_var_95),
                'expected_loss': float(np.mean(crash_returns[crash_returns < 0])) if np.any(crash_returns < 0) else 0,
                'probability_of_loss': float(np.mean(crash_returns < 0))
            },
            'volatility_spike_2x': {
                'scenario_description': 'Volatility doubled',
                'var_95': float(vol_spike_var_95),
                'expected_loss': float(np.mean(high_vol_returns[high_vol_returns < 0])) if np.any(high_vol_returns < 0) else 0,
                'probability_of_loss': float(np.mean(high_vol_returns < 0))
            },
            'trend_reversal': {
                'scenario_description': 'Complete trend reversal',
                'var_95': float(reversal_var_95),
                'expected_loss': float(np.mean(trend_reversal_returns[trend_reversal_returns < 0])) if np.any(trend_reversal_returns < 0) else 0,
                'probability_of_loss': float(np.mean(trend_reversal_returns < 0))
            }
        }
        
        return stress_results
    
    def _analyze_tail_risk(self, returns: np.ndarray) -> Dict:
        """Analyze tail risk characteristics"""
        
        # Extreme value analysis
        # Fit Generalized Extreme Value distribution to block maxima
        block_size = 20  # 20-day blocks
        block_maxima = []
        block_minima = []
        
        for i in range(0, len(returns) - block_size + 1, block_size):
            block = returns[i:i + block_size]
            block_maxima.append(np.max(block))
            block_minima.append(np.min(block))
        
        if len(block_maxima) > 10:  # Need sufficient data
            try:
                # Fit GEV to maxima (positive tail)
                gev_params_max = stats.genextreme.fit(block_maxima)
                
                # Fit GEV to minima (negative tail)
                gev_params_min = stats.genextreme.fit([-x for x in block_minima])
                
                tail_analysis = {
                    'extreme_value_analysis': {
                        'positive_tail_shape': float(gev_params_max[0]),
                        'negative_tail_shape': float(gev_params_min[0]),
                        'block_size': block_size,
                        'blocks_analyzed': len(block_maxima)
                    }
                }
            except:
                tail_analysis = {'extreme_value_analysis': 'failed_to_fit'}
        else:
            tail_analysis = {'extreme_value_analysis': 'insufficient_data'}
        
        # Tail ratio analysis
        left_tail_5 = np.percentile(returns, 5)
        left_tail_1 = np.percentile(returns, 1)
        right_tail_95 = np.percentile(returns, 95)
        right_tail_99 = np.percentile(returns, 99)
        
        tail_analysis.update({
            'tail_ratios': {
                'left_tail_ratio_5_1': float(left_tail_5 / left_tail_1) if left_tail_1 != 0 else 0,
                'right_tail_ratio_95_99': float(right_tail_95 / right_tail_99) if right_tail_99 != 0 else 0,
                'tail_asymmetry': float((right_tail_99 + left_tail_1) / (right_tail_99 - left_tail_1)) if (right_tail_99 - left_tail_1) != 0 else 0
            },
            'extreme_observations': {
                'beyond_3_sigma': int(np.sum(np.abs(stats.zscore(returns)) > 3)),
                'beyond_4_sigma': int(np.sum(np.abs(stats.zscore(returns)) > 4)),
                'max_positive_return': float(np.max(returns)),
                'max_negative_return': float(np.min(returns))
            }
        })
        
        return tail_analysis
    
    def _calculate_rolling_risk_metrics(self, returns: np.ndarray) -> Dict:
        """Calculate rolling risk metrics"""
        
        window = self.config['rolling_window_days']
        
        if len(returns) < window * 2:
            return {'note': 'insufficient_data_for_rolling_analysis'}
        
        rolling_vol = []
        rolling_var_95 = []
        rolling_sharpe = []
        
        risk_free_rate = self.config['risk_free_rate'] / 252  # Daily risk-free rate
        
        for i in range(window, len(returns)):
            window_returns = returns[i-window:i]
            
            # Rolling volatility
            vol = np.std(window_returns, ddof=1)
            rolling_vol.append(vol)
            
            # Rolling VaR
            var_95 = np.percentile(window_returns, 5)
            rolling_var_95.append(var_95)
            
            # Rolling Sharpe ratio
            mean_ret = np.mean(window_returns)
            sharpe = (mean_ret - risk_free_rate) / vol if vol > 0 else 0
            rolling_sharpe.append(sharpe)
        
        return {
            'window_size': window,
            'observations': len(rolling_vol),
            'rolling_volatility': {
                'mean': float(np.mean(rolling_vol)),
                'std': float(np.std(rolling_vol)),
                'min': float(np.min(rolling_vol)),
                'max': float(np.max(rolling_vol))
            },
            'rolling_var_95': {
                'mean': float(np.mean(rolling_var_95)),
                'std': float(np.std(rolling_var_95)),
                'min': float(np.min(rolling_var_95)),
                'max': float(np.max(rolling_var_95))
            },
            'rolling_sharpe': {
                'mean': float(np.mean(rolling_sharpe)),
                'std': float(np.std(rolling_sharpe)),
                'min': float(np.min(rolling_sharpe)),
                'max': float(np.max(rolling_sharpe))
            }
        }
    
    def _analyze_risk_attribution(self, returns: np.ndarray, 
                                 prices: Optional[List[float]] = None) -> Dict:
        """Analyze risk attribution"""
        
        if prices is None:
            return {'note': 'price_data_not_provided'}
        
        # Simple risk attribution based on return decomposition
        # This is a simplified version - full attribution would require factor models
        
        price_array = np.array(prices)
        
        if len(price_array) != len(returns) + 1:  # Prices should be one more than returns
            return {'note': 'price_return_length_mismatch'}
        
        # Calculate price-based returns for comparison
        price_returns = np.diff(price_array) / price_array[:-1]
        
        # Attribution analysis
        return {
            'price_return_correlation': float(np.corrcoef(returns, price_returns)[0, 1]) if len(price_returns) == len(returns) else None,
            'excess_return_volatility': float(np.std(returns - price_returns, ddof=1)) if len(price_returns) == len(returns) else None,
            'tracking_error': float(np.std(returns - price_returns, ddof=1)) if len(price_returns) == len(returns) else None
        }
    
    def _assess_overall_risk(self, basic_metrics: Dict, var_analysis: Dict, 
                           drawdown_analysis: Dict, tail_risk: Dict) -> Dict:
        """Assess overall risk level"""
        
        # Risk scoring (0-100, higher = riskier)
        risk_score = 0
        risk_factors = []
        
        # Volatility risk
        annualized_vol = basic_metrics['volatility']['annualized']
        if annualized_vol > 0.5:  # >50% annual volatility
            risk_score += 30
            risk_factors.append('high_volatility')
        elif annualized_vol > 0.3:  # >30% annual volatility
            risk_score += 20
            risk_factors.append('moderate_volatility')
        
        # Drawdown risk
        max_drawdown = abs(drawdown_analysis['max_drawdown'])
        if max_drawdown > 0.3:  # >30% drawdown
            risk_score += 25
            risk_factors.append('high_drawdown')
        elif max_drawdown > 0.15:  # >15% drawdown
            risk_score += 15
            risk_factors.append('moderate_drawdown')
        
        # Tail risk
        if basic_metrics['excess_kurtosis'] > 3:  # High kurtosis
            risk_score += 15
            risk_factors.append('fat_tails')
        
        # Skewness risk
        if abs(basic_metrics['skewness']) > 1:  # High skewness
            risk_score += 10
            risk_factors.append('asymmetric_returns')
        
        # Risk level classification
        if risk_score >= 70:
            risk_level = 'very_high'
        elif risk_score >= 50:
            risk_level = 'high'
        elif risk_score >= 30:
            risk_level = 'moderate'
        elif risk_score >= 15:
            risk_level = 'low'
        else:
            risk_level = 'very_low'
        
        return {
            'risk_score': risk_score,
            'risk_level': risk_level,
            'risk_factors': risk_factors,
            'key_concerns': self._identify_key_concerns(basic_metrics, var_analysis, drawdown_analysis),
            'risk_recommendations': self._generate_risk_recommendations(risk_level, risk_factors)
        }
    
    def _identify_key_concerns(self, basic_metrics: Dict, var_analysis: Dict, 
                              drawdown_analysis: Dict) -> List[str]:
        """Identify key risk concerns"""
        
        concerns = []
        
        # High volatility
        if basic_metrics['volatility']['annualized'] > 0.4:
            concerns.append('Excessive volatility may lead to large losses')
        
        # Large drawdowns
        if abs(drawdown_analysis['max_drawdown']) > 0.25:
            concerns.append('Large drawdowns may be psychologically difficult to endure')
        
        # Long recovery times
        if drawdown_analysis.get('average_recovery_time', 0) > 60:
            concerns.append('Long recovery times from drawdowns')
        
        # Non-normal returns
        if not basic_metrics['jarque_bera_test']['is_normal']:
            concerns.append('Non-normal return distribution increases tail risk')
        
        # High negative skewness
        if basic_metrics['skewness'] < -1:
            concerns.append('Negative skewness indicates higher probability of large losses')
        
        return concerns
    
    def _generate_risk_recommendations(self, risk_level: str, risk_factors: List[str]) -> List[str]:
        """Generate risk management recommendations"""
        
        recommendations = []
        
        if risk_level in ['high', 'very_high']:
            recommendations.append('Consider reducing position sizes')
            recommendations.append('Implement strict stop-loss rules')
            recommendations.append('Monitor risk metrics daily')
        
        if 'high_volatility' in risk_factors:
            recommendations.append('Consider volatility-based position sizing')
        
        if 'high_drawdown' in risk_factors:
            recommendations.append('Implement maximum drawdown limits')
            recommendations.append('Consider portfolio diversification')
        
        if 'fat_tails' in risk_factors:
            recommendations.append('Use non-parametric risk measures')
            recommendations.append('Stress test with extreme scenarios')
        
        if not recommendations:
            recommendations.append('Current risk level appears manageable')
            recommendations.append('Continue monitoring risk metrics regularly')
        
        return recommendations

def main():
    """Test risk analyzer"""
    
    # Create sample return data
    np.random.seed(42)
    returns = np.random.normal(0.1, 2.0, 252)  # Daily returns for one year
    
    # Add some extreme events
    returns[50] = -8.0  # Market crash
    returns[100] = 6.0  # Large positive move
    
    analyzer = RiskAnalyzer()
    
    print("🧪 Testing Risk Analyzer...")
    
    results = analyzer.analyze_strategy_risk(returns.tolist())
    
    print(f"✅ Risk analysis completed")
    print(f"⚠️ Risk level: {results['overall_assessment']['risk_level']}")
    print(f"📊 Risk score: {results['overall_assessment']['risk_score']}/100")
    print(f"📉 Max drawdown: {results['drawdown_analysis']['max_drawdown_pct']:.2f}%")
    print(f"📈 Sharpe ratio: {results['risk_adjusted_performance']['sharpe_ratio']['annualized']:.2f}")

if __name__ == "__main__":
    main()
