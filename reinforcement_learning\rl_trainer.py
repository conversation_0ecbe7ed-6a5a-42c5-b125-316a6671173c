#!/usr/bin/env python3
"""
RL TRAINER - REINFORCEMENT LEARNING TRAINING PIPELINE
====================================================
Main training pipeline that integrates RL agent with realistic backtesting.
Provides complete training workflow with validation and model management.
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import sys
import os
from pathlib import Path

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backtesting'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'data'))
sys.path.append(os.path.dirname(__file__))

from rl_feedback_loop import RLFeedbackLoop
from trading_environment import TradingEnvironment

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class RLTrainer:
    """Complete RL training pipeline"""
    
    def __init__(self, 
                 db_path: str = "data/bitcoin_historical.db",
                 model_save_dir: str = "models",
                 results_dir: str = "results"):
        
        self.db_path = db_path
        self.model_save_dir = Path(model_save_dir)
        self.results_dir = Path(results_dir)
        
        # Create directories
        self.model_save_dir.mkdir(exist_ok=True)
        self.results_dir.mkdir(exist_ok=True)
        
        # Training configuration
        self.config = {
            'training_episodes': 1000,
            'validation_frequency': 100,
            'performance_threshold': 0.05,
            'early_stopping_patience': 3,
            'min_data_points': 1000,
            'train_ratio': 0.7,
            'val_ratio': 0.15,
            'test_ratio': 0.15
        }
        
        logger.info(f"🎯 RL Trainer initialized")
        logger.info(f"💾 Model directory: {self.model_save_dir}")
        logger.info(f"📊 Results directory: {self.results_dir}")
    
    def load_historical_data(self, start_date: str = None, end_date: str = None) -> pd.DataFrame:
        """Load historical data from database"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            if start_date and end_date:
                query = """
                    SELECT timestamp, datetime, open, high, low, close, volume,
                           estimated_spread, volatility, volume_ratio
                    FROM ohlcv_data 
                    WHERE datetime BETWEEN ? AND ?
                    ORDER BY timestamp
                """
                df = pd.read_sql_query(query, conn, params=(start_date, end_date))
            else:
                query = """
                    SELECT timestamp, datetime, open, high, low, close, volume,
                           estimated_spread, volatility, volume_ratio
                    FROM ohlcv_data 
                    ORDER BY timestamp
                """
                df = pd.read_sql_query(query, conn)
            
            conn.close()
            
            if df.empty:
                logger.error(f"❌ No data found in database")
                return pd.DataFrame()
            
            # Convert datetime column
            df['datetime'] = pd.to_datetime(df['datetime'])
            
            logger.info(f"📊 Loaded {len(df):,} records from database")
            return df
            
        except Exception as e:
            logger.error(f"❌ Error loading data: {e}")
            return pd.DataFrame()
    
    def prepare_training_data(self, data: pd.DataFrame) -> Tuple[pd.DataFrame, pd.DataFrame, pd.DataFrame]:
        """Split data into train/validation/test sets"""
        
        if len(data) < self.config['min_data_points']:
            raise ValueError(f"Insufficient data: {len(data)} < {self.config['min_data_points']}")
        
        # Calculate split points
        total_records = len(data)
        train_end = int(total_records * self.config['train_ratio'])
        val_end = int(total_records * (self.config['train_ratio'] + self.config['val_ratio']))
        
        # Split data chronologically
        train_data = data.iloc[:train_end].copy()
        val_data = data.iloc[train_end:val_end].copy()
        test_data = data.iloc[val_end:].copy()
        
        logger.info(f"📊 Data split:")
        logger.info(f"  Train: {len(train_data):,} records ({train_data['datetime'].min()} to {train_data['datetime'].max()})")
        logger.info(f"  Val: {len(val_data):,} records ({val_data['datetime'].min()} to {val_data['datetime'].max()})")
        logger.info(f"  Test: {len(test_data):,} records ({test_data['datetime'].min()} to {test_data['datetime'].max()})")
        
        return train_data, val_data, test_data
    
    def run_training(self, 
                    start_date: str = None, 
                    end_date: str = None,
                    episodes: int = None) -> Dict:
        """Run complete RL training pipeline"""
        
        logger.info(f"🚀 Starting RL training pipeline")
        
        # Override config if specified
        if episodes:
            self.config['training_episodes'] = episodes
        
        # Load data
        data = self.load_historical_data(start_date, end_date)
        if data.empty:
            raise ValueError("No data available for training")
        
        # Prepare training data
        train_data, val_data, test_data = self.prepare_training_data(data)
        
        # Initialize feedback loop with training data
        feedback_loop = RLFeedbackLoop(
            historical_data=train_data,
            validation_frequency=self.config['validation_frequency'],
            performance_threshold=self.config['performance_threshold'],
            max_training_episodes=self.config['training_episodes']
        )
        
        # Run training
        logger.info(f"🎯 Training RL agent for {self.config['training_episodes']} episodes")
        training_summary = feedback_loop.run_continuous_learning()
        
        # Validate on validation set
        logger.info(f"🔍 Running validation on validation set")
        validation_results = self.validate_model(feedback_loop.agent, val_data)
        
        # Test on test set
        logger.info(f"🧪 Running final test on test set")
        test_results = self.test_model(feedback_loop.agent, test_data)
        
        # Combine results
        final_results = {
            'training_summary': training_summary,
            'validation_results': validation_results,
            'test_results': test_results,
            'config': self.config,
            'data_info': {
                'total_records': len(data),
                'train_records': len(train_data),
                'val_records': len(val_data),
                'test_records': len(test_data),
                'date_range': {
                    'start': data['datetime'].min().isoformat(),
                    'end': data['datetime'].max().isoformat()
                }
            }
        }
        
        # Save results
        self.save_training_results(final_results)
        
        logger.info(f"✅ RL training pipeline completed")
        return final_results
    
    def validate_model(self, agent, validation_data: pd.DataFrame) -> Dict:
        """Validate model on validation set"""
        
        try:
            # Create validation environment
            val_env = TradingEnvironment(validation_data, max_episode_steps=1000)
            
            # Run validation episodes
            num_val_episodes = 5
            val_results = []
            
            for episode in range(num_val_episodes):
                state = val_env.reset()
                episode_reward = 0
                
                while True:
                    # Use trained policy (no exploration)
                    if hasattr(agent, 'select_action'):
                        if hasattr(agent, 'ppo_available') and agent.ppo_available:
                            action, _, _ = agent.select_action(state, training=False)
                        else:
                            action = agent.select_action(state)
                    else:
                        action = np.random.randint(0, val_env.action_space_size)
                    
                    next_state, reward, done, info = val_env.step(action)
                    episode_reward += reward
                    state = next_state
                    
                    if done:
                        break
                
                # Get episode summary
                episode_summary = val_env.get_episode_summary()
                val_results.append({
                    'episode': episode,
                    'episode_reward': episode_reward,
                    'portfolio_return': episode_summary.get('total_return_pct', 0),
                    'max_drawdown': episode_summary.get('max_drawdown_pct', 0),
                    'total_trades': episode_summary.get('total_trades', 0),
                    'sharpe_ratio': episode_summary.get('sharpe_ratio', 0)
                })
            
            # Calculate validation metrics
            returns = [r['portfolio_return'] for r in val_results]
            drawdowns = [r['max_drawdown'] for r in val_results]
            trades = [r['total_trades'] for r in val_results]
            
            validation_summary = {
                'num_episodes': num_val_episodes,
                'avg_return': np.mean(returns),
                'std_return': np.std(returns),
                'avg_drawdown': np.mean(drawdowns),
                'avg_trades': np.mean(trades),
                'best_return': max(returns),
                'worst_return': min(returns),
                'episodes': val_results
            }
            
            logger.info(f"📊 Validation Results:")
            logger.info(f"  Average Return: {validation_summary['avg_return']:.2f}%")
            logger.info(f"  Average Drawdown: {validation_summary['avg_drawdown']:.2f}%")
            logger.info(f"  Average Trades: {validation_summary['avg_trades']:.1f}")
            
            return validation_summary
            
        except Exception as e:
            logger.error(f"❌ Validation failed: {e}")
            return {}
    
    def test_model(self, agent, test_data: pd.DataFrame) -> Dict:
        """Test model on test set (final evaluation)"""
        
        try:
            # Create test environment
            test_env = TradingEnvironment(test_data, max_episode_steps=len(test_data)//2)
            
            # Run single long test episode
            state = test_env.reset()
            episode_reward = 0
            step_count = 0
            
            while True:
                # Use trained policy
                if hasattr(agent, 'select_action'):
                    if hasattr(agent, 'ppo_available') and agent.ppo_available:
                        action, _, _ = agent.select_action(state, training=False)
                    else:
                        action = agent.select_action(state)
                else:
                    action = np.random.randint(0, test_env.action_space_size)
                
                next_state, reward, done, info = test_env.step(action)
                episode_reward += reward
                step_count += 1
                state = next_state
                
                if done:
                    break
            
            # Get final test summary
            test_summary = test_env.get_episode_summary()
            
            test_results = {
                'episode_reward': episode_reward,
                'episode_steps': step_count,
                'portfolio_return': test_summary.get('total_return_pct', 0),
                'max_drawdown': test_summary.get('max_drawdown_pct', 0),
                'total_trades': test_summary.get('total_trades', 0),
                'sharpe_ratio': test_summary.get('sharpe_ratio', 0),
                'final_balance': test_summary.get('final_balance', 300)
            }
            
            logger.info(f"🧪 Test Results:")
            logger.info(f"  Portfolio Return: {test_results['portfolio_return']:.2f}%")
            logger.info(f"  Max Drawdown: {test_results['max_drawdown']:.2f}%")
            logger.info(f"  Total Trades: {test_results['total_trades']}")
            logger.info(f"  Sharpe Ratio: {test_results['sharpe_ratio']:.2f}")
            
            return test_results
            
        except Exception as e:
            logger.error(f"❌ Testing failed: {e}")
            return {}
    
    def save_training_results(self, results: Dict):
        """Save training results to file"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        results_file = self.results_dir / f"rl_training_results_{timestamp}.json"
        
        with open(results_file, 'w') as f:
            json.dump(results, f, indent=2, default=str)
        
        logger.info(f"💾 Training results saved: {results_file}")

def main():
    """Main training function"""
    
    parser = argparse.ArgumentParser(description='RL Trading System Trainer')
    parser.add_argument('--episodes', type=int, default=500, help='Number of training episodes')
    parser.add_argument('--start-date', type=str, help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, help='End date (YYYY-MM-DD)')
    parser.add_argument('--db-path', type=str, default='data/bitcoin_historical.db', help='Database path')
    
    args = parser.parse_args()
    
    try:
        # Initialize trainer
        trainer = RLTrainer(db_path=args.db_path)
        
        # Run training
        results = trainer.run_training(
            start_date=args.start_date,
            end_date=args.end_date,
            episodes=args.episodes
        )
        
        print("\n🎉 RL TRAINING COMPLETED!")
        print("="*50)
        print(f"📊 Training Episodes: {results['training_summary']['total_episodes']}")
        print(f"🎯 Best Performance: {results['training_summary']['best_performance']:.2f}%")
        print(f"📈 Test Return: {results['test_results']['portfolio_return']:.2f}%")
        print(f"📉 Test Drawdown: {results['test_results']['max_drawdown']:.2f}%")
        print("="*50)
        
    except Exception as e:
        logger.error(f"❌ Training failed: {e}")
        print(f"❌ Training failed: {e}")

if __name__ == "__main__":
    main()
