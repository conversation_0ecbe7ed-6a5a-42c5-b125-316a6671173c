#!/usr/bin/env python3
"""
PAPER TRADING ENGINE - LIVE SIMULATION SYSTEM
============================================
Implements live paper trading with real-time data feeds,
order management, and performance tracking.
"""

import numpy as np
import pandas as pd
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
import sys
import os
from pathlib import Path
import asyncio
from dataclasses import dataclass
from enum import Enum

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backtesting'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'reinforcement_learning'))

from realistic_trade_simulator import RealTradeSimulator, TradeExecution

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class OrderType(Enum):
    MARKET = "market"
    LIMIT = "limit"
    STOP = "stop"

class OrderSide(Enum):
    BUY = "buy"
    SELL = "sell"

class OrderStatus(Enum):
    PENDING = "pending"
    FILLED = "filled"
    CANCELLED = "cancelled"
    REJECTED = "rejected"

@dataclass
class Order:
    """Order data structure"""
    id: str
    symbol: str
    side: OrderSide
    type: OrderType
    quantity: float
    price: Optional[float]
    status: OrderStatus
    created_at: datetime
    filled_at: Optional[datetime] = None
    filled_price: Optional[float] = None
    filled_quantity: float = 0.0
    commission: float = 0.0

@dataclass
class Position:
    """Position data structure"""
    symbol: str
    quantity: float
    avg_price: float
    unrealized_pnl: float
    realized_pnl: float
    last_update: datetime

@dataclass
class MarketData:
    """Market data structure"""
    symbol: str
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    bid: float
    ask: float
    spread: float

class PaperTradingEngine:
    """
    Live paper trading engine with real-time simulation
    """
    
    def __init__(self, 
                 initial_balance: float = 300.0,
                 commission_rate: float = 0.001,
                 slippage_rate: float = 0.0002):
        
        self.initial_balance = initial_balance
        self.commission_rate = commission_rate
        self.slippage_rate = slippage_rate
        
        # Account state
        self.balance = initial_balance
        self.equity = initial_balance
        self.margin_used = 0.0
        self.margin_available = initial_balance
        
        # Trading state
        self.orders: Dict[str, Order] = {}
        self.positions: Dict[str, Position] = {}
        self.trade_history: List[TradeExecution] = []
        
        # Market data
        self.current_market_data: Dict[str, MarketData] = {}
        self.price_history: Dict[str, List[float]] = {}
        
        # Performance tracking
        self.performance_metrics = {
            'total_trades': 0,
            'winning_trades': 0,
            'losing_trades': 0,
            'total_pnl': 0.0,
            'max_drawdown': 0.0,
            'peak_equity': initial_balance,
            'start_time': datetime.now(),
            'last_update': datetime.now()
        }
        
        # Engine state
        self.running = False
        self.engine_thread = None
        self.data_feed_thread = None
        
        # Strategy integration
        self.trade_simulator = RealTradeSimulator()
        self.strategy_enabled = False
        
        # Results storage
        self.results_dir = Path("results/paper_trading")
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"📄 Paper Trading Engine initialized")
        logger.info(f"💰 Initial balance: ${initial_balance:.2f}")
        logger.info(f"💸 Commission rate: {commission_rate:.3%}")
    
    def start_engine(self):
        """Start the paper trading engine"""
        
        logger.info(f"🚀 Starting paper trading engine")
        
        try:
            self.running = True
            
            # Start engine thread
            self.engine_thread = threading.Thread(target=self._run_engine, daemon=True)
            self.engine_thread.start()
            
            # Start data feed thread
            self.data_feed_thread = threading.Thread(target=self._run_data_feed, daemon=True)
            self.data_feed_thread.start()
            
            logger.info(f"✅ Paper trading engine started")
            
        except Exception as e:
            logger.error(f"❌ Failed to start paper trading engine: {e}")
            raise
    
    def stop_engine(self):
        """Stop the paper trading engine"""
        
        logger.info(f"🛑 Stopping paper trading engine")
        
        try:
            self.running = False
            
            # Wait for threads to finish
            if self.engine_thread and self.engine_thread.is_alive():
                self.engine_thread.join(timeout=10)
            
            if self.data_feed_thread and self.data_feed_thread.is_alive():
                self.data_feed_thread.join(timeout=10)
            
            # Save final state
            self._save_trading_session()
            
            logger.info(f"✅ Paper trading engine stopped")
            
        except Exception as e:
            logger.error(f"❌ Error stopping paper trading engine: {e}")
    
    def _run_engine(self):
        """Main engine loop"""
        
        logger.info(f"⚙️ Paper trading engine loop started")
        
        while self.running:
            try:
                # Process pending orders
                self._process_orders()
                
                # Update positions
                self._update_positions()
                
                # Update performance metrics
                self._update_performance_metrics()
                
                # Run strategy if enabled
                if self.strategy_enabled:
                    self._run_strategy()
                
                # Save state periodically
                if datetime.now().minute % 5 == 0:  # Every 5 minutes
                    self._save_trading_state()
                
                time.sleep(1)  # 1 second update frequency
                
            except Exception as e:
                logger.error(f"❌ Engine loop error: {e}")
                time.sleep(5)
        
        logger.info(f"⚙️ Paper trading engine loop stopped")
    
    def _run_data_feed(self):
        """Simulate real-time data feed"""
        
        logger.info(f"📡 Data feed started")
        
        # Initialize with some base price
        base_price = 50000.0
        
        while self.running:
            try:
                # Simulate market data update
                current_time = datetime.now()
                
                # Generate realistic price movement
                price_change = np.random.normal(0, base_price * 0.001)  # 0.1% volatility
                new_price = base_price + price_change
                base_price = new_price
                
                # Create market data
                market_data = MarketData(
                    symbol="BTCUSDT",
                    timestamp=current_time,
                    open=new_price,
                    high=new_price + abs(np.random.normal(0, base_price * 0.0005)),
                    low=new_price - abs(np.random.normal(0, base_price * 0.0005)),
                    close=new_price,
                    volume=np.random.uniform(1000000, 5000000),
                    bid=new_price - np.random.uniform(1, 10),
                    ask=new_price + np.random.uniform(1, 10),
                    spread=np.random.uniform(5, 20)
                )
                
                # Ensure high >= low
                market_data.high = max(market_data.high, market_data.low)
                
                # Update current market data
                self.current_market_data["BTCUSDT"] = market_data
                
                # Update price history
                if "BTCUSDT" not in self.price_history:
                    self.price_history["BTCUSDT"] = []
                
                self.price_history["BTCUSDT"].append(new_price)
                
                # Keep only last 1000 prices
                if len(self.price_history["BTCUSDT"]) > 1000:
                    self.price_history["BTCUSDT"] = self.price_history["BTCUSDT"][-1000:]
                
                time.sleep(1)  # 1 second data updates
                
            except Exception as e:
                logger.error(f"❌ Data feed error: {e}")
                time.sleep(5)
        
        logger.info(f"📡 Data feed stopped")
    
    def place_order(self, symbol: str, side: OrderSide, order_type: OrderType, 
                   quantity: float, price: Optional[float] = None) -> str:
        """Place a new order"""
        
        try:
            # Generate order ID
            order_id = f"order_{datetime.now().strftime('%Y%m%d_%H%M%S_%f')}"
            
            # Create order
            order = Order(
                id=order_id,
                symbol=symbol,
                side=side,
                type=order_type,
                quantity=quantity,
                price=price,
                status=OrderStatus.PENDING,
                created_at=datetime.now()
            )
            
            # Validate order
            if not self._validate_order(order):
                order.status = OrderStatus.REJECTED
                logger.warning(f"⚠️ Order rejected: {order_id}")
                return order_id
            
            # Add to orders
            self.orders[order_id] = order
            
            logger.info(f"📝 Order placed: {order_id} - {side.value} {quantity} {symbol} @ {price}")
            
            return order_id
            
        except Exception as e:
            logger.error(f"❌ Error placing order: {e}")
            raise
    
    def cancel_order(self, order_id: str) -> bool:
        """Cancel an existing order"""
        
        try:
            if order_id in self.orders:
                order = self.orders[order_id]
                if order.status == OrderStatus.PENDING:
                    order.status = OrderStatus.CANCELLED
                    logger.info(f"❌ Order cancelled: {order_id}")
                    return True
                else:
                    logger.warning(f"⚠️ Cannot cancel order {order_id} - status: {order.status}")
                    return False
            else:
                logger.warning(f"⚠️ Order not found: {order_id}")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error cancelling order: {e}")
            return False
    
    def _validate_order(self, order: Order) -> bool:
        """Validate order before placement"""
        
        # Check if we have market data
        if order.symbol not in self.current_market_data:
            logger.warning(f"⚠️ No market data for {order.symbol}")
            return False
        
        # Check balance for buy orders
        if order.side == OrderSide.BUY:
            market_data = self.current_market_data[order.symbol]
            estimated_cost = order.quantity * (order.price or market_data.ask)
            estimated_commission = estimated_cost * self.commission_rate
            
            if estimated_cost + estimated_commission > self.margin_available:
                logger.warning(f"⚠️ Insufficient balance for order")
                return False
        
        # Check position for sell orders
        if order.side == OrderSide.SELL:
            position = self.positions.get(order.symbol)
            if not position or position.quantity < order.quantity:
                logger.warning(f"⚠️ Insufficient position for sell order")
                return False
        
        return True
    
    def _process_orders(self):
        """Process pending orders"""
        
        for order_id, order in list(self.orders.items()):
            if order.status == OrderStatus.PENDING:
                self._try_fill_order(order)
    
    def _try_fill_order(self, order: Order):
        """Try to fill a pending order"""
        
        try:
            if order.symbol not in self.current_market_data:
                return
            
            market_data = self.current_market_data[order.symbol]
            
            # Determine fill conditions
            should_fill = False
            fill_price = None
            
            if order.type == OrderType.MARKET:
                # Market orders fill immediately
                should_fill = True
                fill_price = market_data.ask if order.side == OrderSide.BUY else market_data.bid
                
            elif order.type == OrderType.LIMIT:
                # Limit orders fill when price is reached
                if order.side == OrderSide.BUY and market_data.ask <= order.price:
                    should_fill = True
                    fill_price = order.price
                elif order.side == OrderSide.SELL and market_data.bid >= order.price:
                    should_fill = True
                    fill_price = order.price
            
            if should_fill:
                self._fill_order(order, fill_price)
                
        except Exception as e:
            logger.error(f"❌ Error processing order {order.id}: {e}")
    
    def _fill_order(self, order: Order, fill_price: float):
        """Fill an order"""
        
        try:
            # Apply slippage
            if order.type == OrderType.MARKET:
                slippage = fill_price * self.slippage_rate
                if order.side == OrderSide.BUY:
                    fill_price += slippage
                else:
                    fill_price -= slippage
            
            # Calculate commission
            trade_value = order.quantity * fill_price
            commission = trade_value * self.commission_rate
            
            # Update order
            order.status = OrderStatus.FILLED
            order.filled_at = datetime.now()
            order.filled_price = fill_price
            order.filled_quantity = order.quantity
            order.commission = commission
            
            # Update position
            self._update_position(order)
            
            # Update balance
            if order.side == OrderSide.BUY:
                self.balance -= (trade_value + commission)
            else:
                self.balance += (trade_value - commission)
            
            # Create trade execution record
            execution = TradeExecution(
                timestamp=order.filled_at,
                symbol=order.symbol,
                side=order.side.value,
                quantity=order.quantity,
                price=fill_price,
                commission=commission,
                slippage=abs(fill_price - (order.price or fill_price)),
                order_id=order.id
            )
            
            self.trade_history.append(execution)
            self.performance_metrics['total_trades'] += 1
            
            logger.info(f"✅ Order filled: {order.id} - {order.quantity} @ ${fill_price:.2f}")
            
        except Exception as e:
            logger.error(f"❌ Error filling order {order.id}: {e}")
    
    def _update_position(self, order: Order):
        """Update position after order fill"""
        
        symbol = order.symbol
        
        if symbol not in self.positions:
            self.positions[symbol] = Position(
                symbol=symbol,
                quantity=0.0,
                avg_price=0.0,
                unrealized_pnl=0.0,
                realized_pnl=0.0,
                last_update=datetime.now()
            )
        
        position = self.positions[symbol]
        
        if order.side == OrderSide.BUY:
            # Add to position
            total_cost = position.quantity * position.avg_price + order.quantity * order.filled_price
            total_quantity = position.quantity + order.quantity
            
            if total_quantity > 0:
                position.avg_price = total_cost / total_quantity
            
            position.quantity = total_quantity
            
        else:  # SELL
            # Reduce position and calculate realized PnL
            realized_pnl = order.quantity * (order.filled_price - position.avg_price)
            position.realized_pnl += realized_pnl
            position.quantity -= order.quantity
            
            # Update performance metrics
            if realized_pnl > 0:
                self.performance_metrics['winning_trades'] += 1
            else:
                self.performance_metrics['losing_trades'] += 1
            
            self.performance_metrics['total_pnl'] += realized_pnl
        
        position.last_update = datetime.now()
    
    def _update_positions(self):
        """Update unrealized PnL for all positions"""
        
        for symbol, position in self.positions.items():
            if symbol in self.current_market_data and position.quantity != 0:
                current_price = self.current_market_data[symbol].close
                position.unrealized_pnl = position.quantity * (current_price - position.avg_price)
    
    def _update_performance_metrics(self):
        """Update performance metrics"""
        
        # Calculate current equity
        total_unrealized_pnl = sum(pos.unrealized_pnl for pos in self.positions.values())
        self.equity = self.balance + total_unrealized_pnl
        
        # Update peak equity and drawdown
        if self.equity > self.performance_metrics['peak_equity']:
            self.performance_metrics['peak_equity'] = self.equity
        
        current_drawdown = (self.performance_metrics['peak_equity'] - self.equity) / self.performance_metrics['peak_equity']
        if current_drawdown > self.performance_metrics['max_drawdown']:
            self.performance_metrics['max_drawdown'] = current_drawdown
        
        self.performance_metrics['last_update'] = datetime.now()
    
    def _run_strategy(self):
        """Run automated trading strategy"""
        
        try:
            # This would integrate with the RL strategy
            # For now, implement a simple grid strategy
            
            if "BTCUSDT" in self.current_market_data:
                market_data = self.current_market_data["BTCUSDT"]
                
                # Simple grid logic (placeholder)
                current_price = market_data.close
                
                # Check if we should place grid orders
                # This is a simplified version - full implementation would use the RL agent
                
                pass  # Strategy implementation would go here
                
        except Exception as e:
            logger.error(f"❌ Strategy execution error: {e}")
    
    def _save_trading_state(self):
        """Save current trading state"""
        
        try:
            state = {
                'timestamp': datetime.now().isoformat(),
                'balance': self.balance,
                'equity': self.equity,
                'positions': {symbol: {
                    'quantity': pos.quantity,
                    'avg_price': pos.avg_price,
                    'unrealized_pnl': pos.unrealized_pnl,
                    'realized_pnl': pos.realized_pnl
                } for symbol, pos in self.positions.items()},
                'performance_metrics': self.performance_metrics,
                'active_orders': len([o for o in self.orders.values() if o.status == OrderStatus.PENDING])
            }
            
            state_file = self.results_dir / "current_state.json"
            with open(state_file, 'w') as f:
                json.dump(state, f, indent=2, default=str)
                
        except Exception as e:
            logger.error(f"❌ Error saving trading state: {e}")
    
    def _save_trading_session(self):
        """Save complete trading session"""
        
        try:
            session = {
                'session_start': self.performance_metrics['start_time'].isoformat(),
                'session_end': datetime.now().isoformat(),
                'initial_balance': self.initial_balance,
                'final_balance': self.balance,
                'final_equity': self.equity,
                'total_return': (self.equity - self.initial_balance) / self.initial_balance,
                'performance_metrics': self.performance_metrics,
                'trade_history': [
                    {
                        'timestamp': trade.timestamp.isoformat(),
                        'symbol': trade.symbol,
                        'side': trade.side,
                        'quantity': trade.quantity,
                        'price': trade.price,
                        'commission': trade.commission,
                        'slippage': trade.slippage
                    } for trade in self.trade_history
                ],
                'final_positions': {symbol: {
                    'quantity': pos.quantity,
                    'avg_price': pos.avg_price,
                    'unrealized_pnl': pos.unrealized_pnl,
                    'realized_pnl': pos.realized_pnl
                } for symbol, pos in self.positions.items()}
            }
            
            session_file = self.results_dir / f"trading_session_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(session_file, 'w') as f:
                json.dump(session, f, indent=2, default=str)
            
            logger.info(f"💾 Trading session saved: {session_file}")
            
        except Exception as e:
            logger.error(f"❌ Error saving trading session: {e}")
    
    def get_account_summary(self) -> Dict:
        """Get current account summary"""
        
        return {
            'balance': self.balance,
            'equity': self.equity,
            'margin_used': self.margin_used,
            'margin_available': self.margin_available,
            'total_return': (self.equity - self.initial_balance) / self.initial_balance,
            'total_return_pct': ((self.equity - self.initial_balance) / self.initial_balance) * 100,
            'unrealized_pnl': sum(pos.unrealized_pnl for pos in self.positions.values()),
            'realized_pnl': sum(pos.realized_pnl for pos in self.positions.values()),
            'performance_metrics': self.performance_metrics
        }
    
    def get_positions(self) -> Dict[str, Dict]:
        """Get current positions"""
        
        return {symbol: {
            'quantity': pos.quantity,
            'avg_price': pos.avg_price,
            'unrealized_pnl': pos.unrealized_pnl,
            'realized_pnl': pos.realized_pnl,
            'last_update': pos.last_update.isoformat()
        } for symbol, pos in self.positions.items() if pos.quantity != 0}
    
    def get_orders(self, status: Optional[OrderStatus] = None) -> List[Dict]:
        """Get orders, optionally filtered by status"""
        
        orders = list(self.orders.values())
        
        if status:
            orders = [o for o in orders if o.status == status]
        
        return [{
            'id': order.id,
            'symbol': order.symbol,
            'side': order.side.value,
            'type': order.type.value,
            'quantity': order.quantity,
            'price': order.price,
            'status': order.status.value,
            'created_at': order.created_at.isoformat(),
            'filled_at': order.filled_at.isoformat() if order.filled_at else None,
            'filled_price': order.filled_price,
            'commission': order.commission
        } for order in orders]

def main():
    """Test paper trading engine"""
    
    print("🧪 Testing Paper Trading Engine...")
    
    try:
        # Create engine
        engine = PaperTradingEngine(initial_balance=1000.0)
        
        print(f"✅ Paper trading engine created")
        print(f"💰 Initial balance: ${engine.balance:.2f}")
        
        # Start engine
        engine.start_engine()
        print(f"🚀 Engine started")
        
        # Wait a bit for data feed
        time.sleep(3)
        
        # Place a test order
        if "BTCUSDT" in engine.current_market_data:
            market_data = engine.current_market_data["BTCUSDT"]
            order_id = engine.place_order(
                symbol="BTCUSDT",
                side=OrderSide.BUY,
                order_type=OrderType.MARKET,
                quantity=0.001
            )
            print(f"📝 Test order placed: {order_id}")
        
        # Wait for order processing
        time.sleep(2)
        
        # Get account summary
        summary = engine.get_account_summary()
        print(f"📊 Account summary:")
        print(f"  Balance: ${summary['balance']:.2f}")
        print(f"  Equity: ${summary['equity']:.2f}")
        print(f"  Total trades: {summary['performance_metrics']['total_trades']}")
        
        # Stop engine
        engine.stop_engine()
        print(f"🛑 Engine stopped")
        
        print("🎉 Paper trading engine test completed!")
        
    except Exception as e:
        print(f"❌ Paper trading engine test failed: {e}")

if __name__ == "__main__":
    main()
