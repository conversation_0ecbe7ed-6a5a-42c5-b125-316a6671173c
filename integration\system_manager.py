#!/usr/bin/env python3
"""
SYSTEM MANAGER - COMPLETE SYSTEM INTEGRATION
===========================================
Manages the complete trading system integration including:
- Data collection and management
- Strategy training and validation
- Live paper trading
- Performance monitoring
- System health checks
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import threading
import time
import schedule
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
import sys
import os
from pathlib import Path
import asyncio
from concurrent.futures import ThreadPoolExecutor

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'data'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backtesting'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'reinforcement_learning'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'validation'))

from historical_data_collector import HistoricalDataCollector
from enhanced_backtester import EnhancedBacktester
from rl_trainer import RLTrainer
from comprehensive_validator import ComprehensiveValidator

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class SystemManager:
    """
    Complete trading system integration and management
    """
    
    def __init__(self, config_path: str = "config/system_config.json"):
        self.config_path = config_path
        self.config = self._load_config()
        
        # Initialize system components
        self.data_collector = HistoricalDataCollector()
        self.backtester = EnhancedBacktester()
        self.rl_trainer = RLTrainer()
        self.validator = ComprehensiveValidator()
        
        # System state
        self.system_status = {
            'status': 'initializing',
            'last_update': datetime.now().isoformat(),
            'components': {
                'data_collector': 'ready',
                'backtester': 'ready',
                'rl_trainer': 'ready',
                'validator': 'ready'
            },
            'active_strategies': [],
            'performance_metrics': {}
        }
        
        # Threading and scheduling
        self.executor = ThreadPoolExecutor(max_workers=4)
        self.running = False
        self.scheduler_thread = None
        
        # Results directories
        self.results_dir = Path("results/system")
        self.logs_dir = Path("logs")
        self.models_dir = Path("models")
        
        # Create directories
        for directory in [self.results_dir, self.logs_dir, self.models_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"🎯 System Manager initialized")
        logger.info(f"📊 Configuration loaded from {config_path}")
        logger.info(f"🔧 Components: {len(self.system_status['components'])} ready")
    
    def _load_config(self) -> Dict:
        """Load system configuration"""
        
        default_config = {
            'data_collection': {
                'update_frequency_minutes': 60,
                'symbols': ['BTCUSDT'],
                'timeframes': ['1m'],
                'max_history_days': 365
            },
            'training': {
                'retrain_frequency_hours': 24,
                'validation_frequency_hours': 6,
                'min_training_data_days': 30,
                'max_training_episodes': 500
            },
            'paper_trading': {
                'enabled': True,
                'initial_balance': 300.0,
                'max_position_size': 10.0,
                'risk_limit_pct': 20.0,
                'update_frequency_minutes': 5
            },
            'monitoring': {
                'health_check_frequency_minutes': 15,
                'performance_report_frequency_hours': 6,
                'alert_thresholds': {
                    'max_drawdown_pct': 15.0,
                    'min_sharpe_ratio': 0.5,
                    'max_consecutive_losses': 5
                }
            },
            'system': {
                'auto_start': True,
                'log_level': 'INFO',
                'backup_frequency_hours': 12,
                'cleanup_old_data_days': 90
            }
        }
        
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r') as f:
                    user_config = json.load(f)
                
                # Merge with defaults
                config = {**default_config, **user_config}
                logger.info(f"✅ Configuration loaded from {self.config_path}")
            else:
                config = default_config
                # Save default config
                os.makedirs(os.path.dirname(self.config_path), exist_ok=True)
                with open(self.config_path, 'w') as f:
                    json.dump(config, f, indent=2)
                logger.info(f"📝 Default configuration saved to {self.config_path}")
            
            return config
            
        except Exception as e:
            logger.error(f"❌ Error loading configuration: {e}")
            return default_config
    
    def start_system(self):
        """Start the complete trading system"""
        
        logger.info(f"🚀 Starting complete trading system")
        
        try:
            # Update system status
            self.system_status['status'] = 'starting'
            self.system_status['last_update'] = datetime.now().isoformat()
            
            # Initialize data collection
            self._initialize_data_collection()
            
            # Setup scheduling
            self._setup_scheduling()
            
            # Start scheduler thread
            self.running = True
            self.scheduler_thread = threading.Thread(target=self._run_scheduler, daemon=True)
            self.scheduler_thread.start()
            
            # Start paper trading if enabled
            if self.config['paper_trading']['enabled']:
                self._start_paper_trading()
            
            # Update status
            self.system_status['status'] = 'running'
            self.system_status['last_update'] = datetime.now().isoformat()
            
            logger.info(f"✅ Trading system started successfully")
            
            # Save initial status
            self._save_system_status()
            
        except Exception as e:
            logger.error(f"❌ Failed to start system: {e}")
            self.system_status['status'] = 'error'
            self.system_status['error'] = str(e)
            raise
    
    def stop_system(self):
        """Stop the trading system gracefully"""
        
        logger.info(f"🛑 Stopping trading system")
        
        try:
            # Set running flag to False
            self.running = False
            
            # Wait for scheduler thread to finish
            if self.scheduler_thread and self.scheduler_thread.is_alive():
                self.scheduler_thread.join(timeout=10)
            
            # Shutdown executor
            self.executor.shutdown(wait=True)
            
            # Update status
            self.system_status['status'] = 'stopped'
            self.system_status['last_update'] = datetime.now().isoformat()
            
            # Save final status
            self._save_system_status()
            
            logger.info(f"✅ Trading system stopped successfully")
            
        except Exception as e:
            logger.error(f"❌ Error stopping system: {e}")
    
    def _initialize_data_collection(self):
        """Initialize historical data collection"""
        
        logger.info(f"📊 Initializing data collection")
        
        try:
            # Check if we have recent data
            last_update = self.data_collector.get_last_update_time()
            
            if last_update is None or (datetime.now() - last_update).days > 1:
                logger.info(f"🔄 Collecting initial historical data")
                
                # Collect historical data
                success = self.data_collector.collect_historical_data(
                    days_back=self.config['data_collection']['max_history_days']
                )
                
                if success:
                    logger.info(f"✅ Historical data collection completed")
                    self.system_status['components']['data_collector'] = 'ready'
                else:
                    logger.error(f"❌ Historical data collection failed")
                    self.system_status['components']['data_collector'] = 'error'
            else:
                logger.info(f"✅ Recent data available, skipping initial collection")
                self.system_status['components']['data_collector'] = 'ready'
                
        except Exception as e:
            logger.error(f"❌ Data collection initialization failed: {e}")
            self.system_status['components']['data_collector'] = 'error'
    
    def _setup_scheduling(self):
        """Setup automated scheduling for system tasks"""
        
        logger.info(f"⏰ Setting up task scheduling")
        
        # Data collection schedule
        schedule.every(self.config['data_collection']['update_frequency_minutes']).minutes.do(
            self._scheduled_data_update
        )
        
        # Training schedule
        schedule.every(self.config['training']['retrain_frequency_hours']).hours.do(
            self._scheduled_training
        )
        
        # Validation schedule
        schedule.every(self.config['training']['validation_frequency_hours']).hours.do(
            self._scheduled_validation
        )
        
        # Health check schedule
        schedule.every(self.config['monitoring']['health_check_frequency_minutes']).minutes.do(
            self._scheduled_health_check
        )
        
        # Performance report schedule
        schedule.every(self.config['monitoring']['performance_report_frequency_hours']).hours.do(
            self._scheduled_performance_report
        )
        
        # Backup schedule
        schedule.every(self.config['system']['backup_frequency_hours']).hours.do(
            self._scheduled_backup
        )
        
        logger.info(f"✅ Scheduling configured")
    
    def _run_scheduler(self):
        """Run the task scheduler"""
        
        logger.info(f"⏰ Starting task scheduler")
        
        while self.running:
            try:
                schedule.run_pending()
                time.sleep(60)  # Check every minute
            except Exception as e:
                logger.error(f"❌ Scheduler error: {e}")
                time.sleep(60)
        
        logger.info(f"⏰ Task scheduler stopped")
    
    def _scheduled_data_update(self):
        """Scheduled data collection update"""
        
        logger.info(f"📊 Running scheduled data update")
        
        try:
            # Submit data update task
            future = self.executor.submit(self._update_data)
            
            # Don't wait for completion to avoid blocking scheduler
            logger.info(f"📊 Data update task submitted")
            
        except Exception as e:
            logger.error(f"❌ Failed to schedule data update: {e}")
    
    def _scheduled_training(self):
        """Scheduled model training"""
        
        logger.info(f"🤖 Running scheduled training")
        
        try:
            # Submit training task
            future = self.executor.submit(self._train_models)
            
            logger.info(f"🤖 Training task submitted")
            
        except Exception as e:
            logger.error(f"❌ Failed to schedule training: {e}")
    
    def _scheduled_validation(self):
        """Scheduled validation"""
        
        logger.info(f"🔬 Running scheduled validation")
        
        try:
            # Submit validation task
            future = self.executor.submit(self._validate_strategies)
            
            logger.info(f"🔬 Validation task submitted")
            
        except Exception as e:
            logger.error(f"❌ Failed to schedule validation: {e}")
    
    def _scheduled_health_check(self):
        """Scheduled system health check"""
        
        logger.info(f"🏥 Running scheduled health check")
        
        try:
            self._perform_health_check()
            
        except Exception as e:
            logger.error(f"❌ Health check failed: {e}")
    
    def _scheduled_performance_report(self):
        """Scheduled performance report"""
        
        logger.info(f"📈 Running scheduled performance report")
        
        try:
            # Submit performance report task
            future = self.executor.submit(self._generate_performance_report)
            
            logger.info(f"📈 Performance report task submitted")
            
        except Exception as e:
            logger.error(f"❌ Failed to schedule performance report: {e}")
    
    def _scheduled_backup(self):
        """Scheduled system backup"""
        
        logger.info(f"💾 Running scheduled backup")
        
        try:
            # Submit backup task
            future = self.executor.submit(self._backup_system)
            
            logger.info(f"💾 Backup task submitted")
            
        except Exception as e:
            logger.error(f"❌ Failed to schedule backup: {e}")
    
    def _update_data(self):
        """Update historical data"""
        
        try:
            logger.info(f"📊 Updating historical data")
            
            success = self.data_collector.update_data()
            
            if success:
                logger.info(f"✅ Data update completed")
                self.system_status['components']['data_collector'] = 'ready'
            else:
                logger.error(f"❌ Data update failed")
                self.system_status['components']['data_collector'] = 'error'
            
            self._save_system_status()
            
        except Exception as e:
            logger.error(f"❌ Data update error: {e}")
            self.system_status['components']['data_collector'] = 'error'
    
    def _train_models(self):
        """Train RL models"""
        
        try:
            logger.info(f"🤖 Training RL models")
            
            # Check if we have enough data
            data_age = self._get_data_age()
            if data_age > self.config['training']['min_training_data_days']:
                
                # Run RL training
                results = self.rl_trainer.run_training(
                    episodes=self.config['training']['max_training_episodes']
                )
                
                if results:
                    logger.info(f"✅ Model training completed")
                    self.system_status['components']['rl_trainer'] = 'ready'
                    
                    # Update active strategies
                    self.system_status['active_strategies'].append({
                        'name': 'rl_enhanced_grid',
                        'trained_at': datetime.now().isoformat(),
                        'performance': results.get('test_results', {})
                    })
                else:
                    logger.error(f"❌ Model training failed")
                    self.system_status['components']['rl_trainer'] = 'error'
            else:
                logger.warning(f"⚠️ Insufficient training data: {data_age} days")
            
            self._save_system_status()
            
        except Exception as e:
            logger.error(f"❌ Model training error: {e}")
            self.system_status['components']['rl_trainer'] = 'error'
    
    def _validate_strategies(self):
        """Validate trading strategies"""
        
        try:
            logger.info(f"🔬 Validating strategies")
            
            # Run comprehensive validation
            end_date = datetime.now().strftime('%Y-%m-%d')
            start_date = (datetime.now() - timedelta(days=90)).strftime('%Y-%m-%d')
            
            results = self.validator.run_comprehensive_validation(
                strategy_name='rl_enhanced_grid',
                start_date=start_date,
                end_date=end_date
            )
            
            if results:
                logger.info(f"✅ Strategy validation completed")
                self.system_status['components']['validator'] = 'ready'
                
                # Update performance metrics
                if 'overall_assessment' in results:
                    assessment = results['overall_assessment']
                    self.system_status['performance_metrics'] = {
                        'validation_score': assessment.get('validation_score', 0),
                        'validation_grade': assessment.get('validation_grade', 'F'),
                        'risk_level': assessment.get('risk_level', 'unknown'),
                        'implementation_ready': assessment.get('implementation_ready', False),
                        'last_validation': datetime.now().isoformat()
                    }
            else:
                logger.error(f"❌ Strategy validation failed")
                self.system_status['components']['validator'] = 'error'
            
            self._save_system_status()
            
        except Exception as e:
            logger.error(f"❌ Strategy validation error: {e}")
            self.system_status['components']['validator'] = 'error'
    
    def _perform_health_check(self):
        """Perform system health check"""
        
        try:
            health_status = {
                'timestamp': datetime.now().isoformat(),
                'overall_health': 'healthy',
                'components': {},
                'alerts': []
            }
            
            # Check data freshness
            data_age = self._get_data_age()
            if data_age > 1:  # More than 1 day old
                health_status['components']['data'] = 'stale'
                health_status['alerts'].append(f"Data is {data_age} days old")
            else:
                health_status['components']['data'] = 'fresh'
            
            # Check disk space
            disk_usage = self._check_disk_usage()
            if disk_usage > 90:  # More than 90% full
                health_status['components']['disk'] = 'critical'
                health_status['alerts'].append(f"Disk usage at {disk_usage}%")
                health_status['overall_health'] = 'critical'
            elif disk_usage > 80:  # More than 80% full
                health_status['components']['disk'] = 'warning'
                health_status['alerts'].append(f"Disk usage at {disk_usage}%")
                health_status['overall_health'] = 'warning'
            else:
                health_status['components']['disk'] = 'healthy'
            
            # Check component status
            error_components = [name for name, status in self.system_status['components'].items() 
                             if status == 'error']
            
            if error_components:
                health_status['components']['system'] = 'error'
                health_status['alerts'].append(f"Components with errors: {error_components}")
                health_status['overall_health'] = 'unhealthy'
            else:
                health_status['components']['system'] = 'healthy'
            
            # Save health status
            health_file = self.results_dir / f"health_check_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(health_file, 'w') as f:
                json.dump(health_status, f, indent=2)
            
            # Log alerts
            for alert in health_status['alerts']:
                logger.warning(f"🚨 Health Alert: {alert}")
            
            if health_status['overall_health'] == 'healthy':
                logger.info(f"✅ System health check passed")
            else:
                logger.warning(f"⚠️ System health check found issues")
            
        except Exception as e:
            logger.error(f"❌ Health check error: {e}")
    
    def _generate_performance_report(self):
        """Generate performance report"""
        
        try:
            logger.info(f"📈 Generating performance report")
            
            report = {
                'timestamp': datetime.now().isoformat(),
                'system_status': self.system_status,
                'performance_summary': self._calculate_performance_summary(),
                'recent_activity': self._get_recent_activity(),
                'recommendations': self._generate_recommendations()
            }
            
            # Save report
            report_file = self.results_dir / f"performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w') as f:
                json.dump(report, f, indent=2)
            
            logger.info(f"✅ Performance report generated: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ Performance report error: {e}")
    
    def _backup_system(self):
        """Backup system data and models"""
        
        try:
            logger.info(f"💾 Creating system backup")
            
            backup_dir = Path(f"backups/backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}")
            backup_dir.mkdir(parents=True, exist_ok=True)
            
            # Backup database
            if os.path.exists("data/bitcoin_historical.db"):
                import shutil
                shutil.copy2("data/bitcoin_historical.db", backup_dir / "bitcoin_historical.db")
            
            # Backup models
            if self.models_dir.exists():
                shutil.copytree(self.models_dir, backup_dir / "models", dirs_exist_ok=True)
            
            # Backup configuration
            if os.path.exists(self.config_path):
                shutil.copy2(self.config_path, backup_dir / "system_config.json")
            
            # Backup system status
            with open(backup_dir / "system_status.json", 'w') as f:
                json.dump(self.system_status, f, indent=2)
            
            logger.info(f"✅ System backup completed: {backup_dir}")
            
        except Exception as e:
            logger.error(f"❌ System backup error: {e}")
    
    def _start_paper_trading(self):
        """Start paper trading system"""
        
        logger.info(f"📄 Starting paper trading system")
        
        try:
            # This would integrate with the paper trading system
            # For now, just log that it's enabled
            logger.info(f"✅ Paper trading system ready")
            
        except Exception as e:
            logger.error(f"❌ Paper trading startup error: {e}")
    
    def _get_data_age(self) -> int:
        """Get age of data in days"""
        
        try:
            last_update = self.data_collector.get_last_update_time()
            if last_update:
                return (datetime.now() - last_update).days
            else:
                return 999  # Very old
        except:
            return 999
    
    def _check_disk_usage(self) -> float:
        """Check disk usage percentage"""
        
        try:
            import shutil
            total, used, free = shutil.disk_usage(".")
            return (used / total) * 100
        except:
            return 0
    
    def _calculate_performance_summary(self) -> Dict:
        """Calculate performance summary"""
        
        return {
            'active_strategies': len(self.system_status['active_strategies']),
            'system_uptime_hours': self._get_system_uptime(),
            'last_training': self._get_last_training_time(),
            'last_validation': self._get_last_validation_time(),
            'data_freshness_hours': self._get_data_age() * 24
        }
    
    def _get_recent_activity(self) -> List[Dict]:
        """Get recent system activity"""
        
        # This would query logs and recent operations
        return [
            {
                'timestamp': datetime.now().isoformat(),
                'activity': 'system_health_check',
                'status': 'completed'
            }
        ]
    
    def _generate_recommendations(self) -> List[str]:
        """Generate system recommendations"""
        
        recommendations = []
        
        # Check data age
        data_age = self._get_data_age()
        if data_age > 1:
            recommendations.append(f"Update historical data (last update: {data_age} days ago)")
        
        # Check validation status
        if not self.system_status['performance_metrics'].get('implementation_ready', False):
            recommendations.append("Strategy validation shows issues - review before deployment")
        
        # Check component status
        error_components = [name for name, status in self.system_status['components'].items() 
                          if status == 'error']
        if error_components:
            recommendations.append(f"Fix component errors: {error_components}")
        
        if not recommendations:
            recommendations.append("System operating normally")
        
        return recommendations
    
    def _get_system_uptime(self) -> float:
        """Get system uptime in hours"""
        
        # This would track actual uptime
        return 24.0  # Placeholder
    
    def _get_last_training_time(self) -> Optional[str]:
        """Get last training time"""
        
        if self.system_status['active_strategies']:
            return self.system_status['active_strategies'][-1].get('trained_at')
        return None
    
    def _get_last_validation_time(self) -> Optional[str]:
        """Get last validation time"""
        
        return self.system_status['performance_metrics'].get('last_validation')
    
    def _save_system_status(self):
        """Save current system status"""
        
        try:
            status_file = self.results_dir / "system_status.json"
            with open(status_file, 'w') as f:
                json.dump(self.system_status, f, indent=2)
        except Exception as e:
            logger.error(f"❌ Failed to save system status: {e}")
    
    def get_system_status(self) -> Dict:
        """Get current system status"""
        
        return self.system_status.copy()
    
    def get_performance_metrics(self) -> Dict:
        """Get current performance metrics"""
        
        return self.system_status['performance_metrics'].copy()

def main():
    """Test system manager"""
    
    print("🧪 Testing System Manager...")
    
    try:
        # Create system manager
        manager = SystemManager()
        
        print(f"✅ System Manager created")
        print(f"📊 Status: {manager.get_system_status()['status']}")
        print(f"🔧 Components: {list(manager.get_system_status()['components'].keys())}")
        
        # Test configuration
        print(f"⚙️ Configuration loaded: {len(manager.config)} sections")
        
        print("🎉 System Manager test completed!")
        
    except Exception as e:
        print(f"❌ System Manager test failed: {e}")

if __name__ == "__main__":
    main()
