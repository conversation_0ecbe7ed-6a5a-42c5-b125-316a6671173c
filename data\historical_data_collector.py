#!/usr/bin/env python3
"""
HISTORICAL DATA COLLECTOR - REAL MARKET DATA PIPELINE
====================================================
Collects real Bitcoin historical data from Binance for realistic backtesting.
Supports 1-minute OHLCV data with volume and spread analysis.
"""

import ccxt
import pandas as pd
import numpy as np
import sqlite3
import time
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import os

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class HistoricalDataCollector:
    """Collects and stores real historical market data from Binance"""
    
    def __init__(self, db_path: str = "data/bitcoin_historical.db"):
        self.db_path = db_path
        self.exchange = None
        self.symbol = 'BTC/USDT'
        self.timeframe = '1m'  # 1-minute bars for realistic simulation
        
        # Ensure data directory exists
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # Initialize database
        self._init_database()
        
        # Initialize exchange connection
        self._init_exchange()
    
    def _init_exchange(self):
        """Initialize Binance exchange connection"""
        try:
            self.exchange = ccxt.binance({
                'enableRateLimit': True,
                'sandbox': False,  # Use real market data
                'options': {
                    'defaultType': 'spot'
                }
            })
            
            # Test connection
            markets = self.exchange.load_markets()
            logger.info(f"✅ Connected to Binance - {len(markets)} markets available")
            
        except Exception as e:
            logger.error(f"❌ Failed to connect to Binance: {e}")
            raise
    
    def _init_database(self):
        """Initialize SQLite database for historical data storage"""
        try:
            conn = sqlite3.connect(self.db_path)
            cursor = conn.cursor()
            
            # Create OHLCV table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS ohlcv_data (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp INTEGER NOT NULL,
                    datetime TEXT NOT NULL,
                    open REAL NOT NULL,
                    high REAL NOT NULL,
                    low REAL NOT NULL,
                    close REAL NOT NULL,
                    volume REAL NOT NULL,
                    symbol TEXT NOT NULL,
                    timeframe TEXT NOT NULL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(timestamp, symbol, timeframe)
                )
            ''')
            
            # Create volume analysis table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS volume_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp INTEGER NOT NULL,
                    symbol TEXT NOT NULL,
                    volume_ma_20 REAL,
                    volume_ratio REAL,
                    price_volume_trend REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(timestamp, symbol)
                )
            ''')
            
            # Create spread analysis table
            cursor.execute('''
                CREATE TABLE IF NOT EXISTS spread_analysis (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    timestamp INTEGER NOT NULL,
                    symbol TEXT NOT NULL,
                    estimated_spread REAL,
                    volatility REAL,
                    volume_impact REAL,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    UNIQUE(timestamp, symbol)
                )
            ''')
            
            conn.commit()
            conn.close()
            logger.info(f"✅ Database initialized: {self.db_path}")
            
        except Exception as e:
            logger.error(f"❌ Database initialization failed: {e}")
            raise
    
    def fetch_historical_data(self, start_date: str, end_date: str, 
                            symbol: str = 'BTC/USDT') -> pd.DataFrame:
        """
        Fetch historical OHLCV data from Binance
        
        Args:
            start_date: Start date in 'YYYY-MM-DD' format
            end_date: End date in 'YYYY-MM-DD' format
            symbol: Trading pair symbol
            
        Returns:
            DataFrame with OHLCV data
        """
        try:
            logger.info(f"📊 Fetching {symbol} data from {start_date} to {end_date}")
            
            # Convert dates to timestamps
            start_ts = int(datetime.strptime(start_date, '%Y-%m-%d').timestamp() * 1000)
            end_ts = int(datetime.strptime(end_date, '%Y-%m-%d').timestamp() * 1000)
            
            all_data = []
            current_ts = start_ts
            
            while current_ts < end_ts:
                try:
                    # Fetch data in chunks (1000 candles max per request)
                    ohlcv = self.exchange.fetch_ohlcv(
                        symbol, self.timeframe, since=current_ts, limit=1000
                    )
                    
                    if not ohlcv:
                        break
                    
                    all_data.extend(ohlcv)
                    current_ts = ohlcv[-1][0] + 60000  # Add 1 minute
                    
                    logger.info(f"📈 Fetched {len(ohlcv)} candles, total: {len(all_data)}")
                    
                    # Rate limiting
                    time.sleep(0.1)
                    
                except Exception as e:
                    logger.warning(f"⚠️ Error fetching chunk: {e}")
                    time.sleep(1)
                    continue
            
            # Convert to DataFrame
            df = pd.DataFrame(all_data, columns=['timestamp', 'open', 'high', 'low', 'close', 'volume'])
            df['datetime'] = pd.to_datetime(df['timestamp'], unit='ms')
            df['symbol'] = symbol
            df['timeframe'] = self.timeframe
            
            logger.info(f"✅ Fetched {len(df)} total candles for {symbol}")
            return df
            
        except Exception as e:
            logger.error(f"❌ Error fetching historical data: {e}")
            raise
    
    def store_data(self, df: pd.DataFrame) -> bool:
        """Store OHLCV data in database"""
        try:
            conn = sqlite3.connect(self.db_path)
            
            # Store OHLCV data
            df.to_sql('ohlcv_data', conn, if_exists='replace', index=False)
            
            conn.close()
            logger.info(f"✅ Stored {len(df)} records in database")
            return True
            
        except Exception as e:
            logger.error(f"❌ Error storing data: {e}")
            return False
    
    def calculate_volume_analysis(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate volume-based metrics for realistic simulation"""
        try:
            logger.info("📊 Calculating volume analysis...")
            
            # Volume moving average
            df['volume_ma_20'] = df['volume'].rolling(window=20).mean()
            
            # Volume ratio (current vs average)
            df['volume_ratio'] = df['volume'] / df['volume_ma_20']
            
            # Price-volume trend
            df['price_change'] = df['close'].pct_change()
            df['price_volume_trend'] = df['price_change'] * df['volume_ratio']
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Error calculating volume analysis: {e}")
            return df
    
    def calculate_spread_analysis(self, df: pd.DataFrame) -> pd.DataFrame:
        """Calculate estimated spread and volatility for slippage modeling"""
        try:
            logger.info("📊 Calculating spread analysis...")
            
            # Estimate spread based on high-low range
            df['volatility'] = (df['high'] - df['low']) / df['close']
            
            # Estimated spread (wider during high volatility, low volume)
            df['estimated_spread'] = (
                df['volatility'] * 0.001 +  # Base volatility component
                (1 / (df['volume'] / 1000000)) * 0.0001  # Volume component
            )
            
            # Cap spread at reasonable levels
            df['estimated_spread'] = df['estimated_spread'].clip(upper=0.002)  # Max 0.2%
            
            # Volume impact factor for slippage
            df['volume_impact'] = 1 / np.sqrt(df['volume'] / df['volume'].median())
            
            return df
            
        except Exception as e:
            logger.error(f"❌ Error calculating spread analysis: {e}")
            return df
    
    def validate_data_quality(self, df: pd.DataFrame) -> Dict:
        """Validate data quality and completeness"""
        try:
            validation_report = {
                'total_records': len(df),
                'date_range': {
                    'start': df['datetime'].min().strftime('%Y-%m-%d %H:%M:%S'),
                    'end': df['datetime'].max().strftime('%Y-%m-%d %H:%M:%S')
                },
                'missing_data': df.isnull().sum().to_dict(),
                'data_gaps': 0,
                'quality_score': 0.0
            }
            
            # Check for data gaps (missing minutes)
            expected_records = (df['timestamp'].max() - df['timestamp'].min()) // 60000 + 1
            actual_records = len(df)
            validation_report['data_gaps'] = expected_records - actual_records
            
            # Calculate quality score
            completeness = actual_records / expected_records if expected_records > 0 else 0
            no_nulls = 1 - (df.isnull().sum().sum() / (len(df) * len(df.columns)))
            validation_report['quality_score'] = (completeness + no_nulls) / 2
            
            logger.info(f"📊 Data Quality Score: {validation_report['quality_score']:.2%}")
            return validation_report
            
        except Exception as e:
            logger.error(f"❌ Error validating data quality: {e}")
            return {}
    
    def collect_full_dataset(self, start_date: str, end_date: str) -> bool:
        """Collect complete dataset with analysis"""
        try:
            logger.info(f"🚀 Starting full data collection: {start_date} to {end_date}")
            
            # 1. Fetch raw OHLCV data
            df = self.fetch_historical_data(start_date, end_date)
            
            if df.empty:
                logger.error("❌ No data fetched")
                return False
            
            # 2. Calculate volume analysis
            df = self.calculate_volume_analysis(df)
            
            # 3. Calculate spread analysis
            df = self.calculate_spread_analysis(df)
            
            # 4. Validate data quality
            quality_report = self.validate_data_quality(df)
            
            # 5. Store in database
            success = self.store_data(df)
            
            if success:
                logger.info(f"✅ Data collection completed successfully")
                logger.info(f"📊 Quality Score: {quality_report.get('quality_score', 0):.2%}")
                logger.info(f"📈 Total Records: {len(df):,}")
                return True
            else:
                logger.error("❌ Failed to store data")
                return False
                
        except Exception as e:
            logger.error(f"❌ Error in full data collection: {e}")
            return False

def main():
    """Main function for data collection"""
    collector = HistoricalDataCollector()
    
    # Collect 2+ years of data for validation
    start_date = "2022-01-01"
    end_date = "2024-12-01"
    
    success = collector.collect_full_dataset(start_date, end_date)
    
    if success:
        print("✅ Historical data collection completed successfully!")
    else:
        print("❌ Historical data collection failed!")

if __name__ == "__main__":
    main()
