#!/usr/bin/env python3
"""
ENHANCED TRADING SYSTEM LAUNCHER
===============================
Main launcher for the enhanced trading system with realistic backtesting.
Provides menu-driven interface for all system components.
"""

import os
import sys
import subprocess
from datetime import datetime
from pathlib import Path

def print_banner():
    """Print system banner"""
    print("\n" + "="*70)
    print("🎯 ENHANCED TRADING SYSTEM WITH REALISTIC BACKTESTING")
    print("="*70)
    print("✅ Real trade simulation with slippage & commissions")
    print("✅ Grid limit orders at exact 0.25% levels")
    print("✅ Statistical validation with out-of-sample testing")
    print("✅ Reinforcement learning feedback loop")
    print("="*70)

def check_dependencies():
    """Check if core dependencies are available"""
    required_modules = ['pandas', 'numpy', 'ccxt']
    missing_modules = []
    
    for module in required_modules:
        try:
            __import__(module)
        except ImportError:
            missing_modules.append(module)
    
    if missing_modules:
        print(f"⚠️ Missing dependencies: {', '.join(missing_modules)}")
        print("📦 Run: pip install -r requirements.txt")
        return False
    
    print("✅ Core dependencies available")
    return True

def run_test_suite():
    """Run the test suite"""
    print("\n🧪 Running Test Suite...")
    try:
        result = subprocess.run([sys.executable, "test_realistic_backtesting.py"], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running tests: {e}")
        return False

def run_data_collection():
    """Run historical data collection"""
    print("\n📊 Starting Historical Data Collection...")
    
    if not Path("data/historical_data_collector.py").exists():
        print("❌ Data collector not found")
        return False
    
    try:
        result = subprocess.run([sys.executable, "data/historical_data_collector.py"], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running data collection: {e}")
        return False

def run_realistic_backtest():
    """Run realistic backtesting"""
    print("\n🎯 Starting Realistic Backtesting...")
    
    if not Path("backtesting/enhanced_backtester.py").exists():
        print("❌ Enhanced backtester not found")
        return False
    
    try:
        result = subprocess.run([sys.executable, "backtesting/enhanced_backtester.py"], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error running backtest: {e}")
        return False

def view_results():
    """View recent results"""
    print("\n📈 Recent Results:")
    
    results_dir = Path("results")
    if not results_dir.exists():
        print("❌ No results directory found")
        return
    
    result_files = list(results_dir.glob("*.json"))
    
    if not result_files:
        print("❌ No result files found")
        return
    
    # Show most recent results
    recent_files = sorted(result_files, key=lambda x: x.stat().st_mtime, reverse=True)[:5]
    
    for i, file in enumerate(recent_files, 1):
        print(f"{i}. {file.name} ({datetime.fromtimestamp(file.stat().st_mtime).strftime('%Y-%m-%d %H:%M')})")

def install_dependencies():
    """Install required dependencies"""
    print("\n📦 Installing Dependencies...")
    
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"], 
                              capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            print(result.stdout)
        else:
            print("❌ Error installing dependencies")
            print(result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def setup_system():
    """Setup the enhanced system"""
    print("\n🔧 Setting Up Enhanced System...")
    
    try:
        result = subprocess.run([sys.executable, "setup_enhanced_system.py"], 
                              capture_output=True, text=True)
        
        print(result.stdout)
        if result.stderr:
            print("Errors:", result.stderr)
        
        return result.returncode == 0
    except Exception as e:
        print(f"❌ Error setting up system: {e}")
        return False

def show_menu():
    """Show main menu"""
    print("\n📋 MAIN MENU:")
    print("1. 🔧 Setup System")
    print("2. 📦 Install Dependencies")
    print("3. 🧪 Run Test Suite")
    print("4. 📊 Collect Historical Data")
    print("5. 🎯 Run Realistic Backtest")
    print("6. 📈 View Results")
    print("7. ❓ System Status")
    print("8. 🚪 Exit")
    print("-" * 30)

def check_system_status():
    """Check system status"""
    print("\n🔍 SYSTEM STATUS:")
    print("-" * 40)
    
    # Check files
    required_files = [
        "data/historical_data_collector.py",
        "backtesting/realistic_trade_simulator.py", 
        "backtesting/enhanced_backtester.py",
        "requirements.txt"
    ]
    
    for file in required_files:
        status = "✅" if Path(file).exists() else "❌"
        print(f"{status} {file}")
    
    # Check directories
    required_dirs = ["data", "backtesting", "results", "config"]
    
    print("\n📁 Directories:")
    for directory in required_dirs:
        status = "✅" if Path(directory).exists() else "❌"
        print(f"{status} {directory}/")
    
    # Check dependencies
    print(f"\n📦 Dependencies: {'✅' if check_dependencies() else '❌'}")

def main():
    """Main launcher function"""
    print_banner()
    
    while True:
        show_menu()
        
        try:
            choice = input("Select option (1-8): ").strip()
            
            if choice == "1":
                setup_system()
            elif choice == "2":
                install_dependencies()
            elif choice == "3":
                run_test_suite()
            elif choice == "4":
                run_data_collection()
            elif choice == "5":
                run_realistic_backtest()
            elif choice == "6":
                view_results()
            elif choice == "7":
                check_system_status()
            elif choice == "8":
                print("\n👋 Goodbye!")
                break
            else:
                print("❌ Invalid choice. Please select 1-8.")
                
        except KeyboardInterrupt:
            print("\n\n👋 Goodbye!")
            break
        except Exception as e:
            print(f"❌ Error: {e}")
        
        input("\nPress Enter to continue...")

if __name__ == "__main__":
    main()
