# Enhanced Trading System with Realistic Backtesting & RL
# Dependencies for real ML models and validation framework

# Core Data Processing
pandas==2.1.0
numpy==1.24.3
scipy>=1.9.0

# Machine Learning
torch>=1.12.0
scikit-learn>=1.1.0
tensorflow>=2.9.0

# Reinforcement Learning
stable-baselines3>=1.6.0
gym>=0.21.0

# Market Data and Trading
ccxt==4.1.0
python-binance>=1.0.0

# Technical Analysis
pandas-ta>=0.3.14b

# Database
sqlalchemy>=1.4.0

# Visualization
matplotlib>=3.5.0
plotly>=5.10.0

# Web Framework
Flask==2.3.3
Werkzeug==2.3.7

# HTTP Requests
requests==2.31.0

# Statistical Analysis
statsmodels>=0.13.0

# Development Tools
pytest>=7.1.0

# Configuration Management
python-dotenv==1.0.0
pyyaml>=6.0

# Logging
loguru>=0.6.0

# Data Storage
h5py>=3.7.0
pyarrow>=9.0.0

# Memory Management
psutil>=5.9.0
