#!/usr/bin/env python3
"""
STATISTICAL VALIDATOR - SIGNIFICANCE TESTING
===========================================
Implements statistical significance testing for trading strategy validation.
Includes hypothesis testing, confidence intervals, and robustness analysis.
"""

import numpy as np
import pandas as pd
from scipy import stats
from scipy.stats import ttest_rel, wilcoxon, jarque_bera, normaltest
import json
from datetime import datetime
from typing import Dict, List, Optional, Tuple, Any
import logging
from pathlib import Path

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class StatisticalValidator:
    """
    Statistical significance testing for trading strategies
    """
    
    def __init__(self, confidence_level: float = 0.95):
        self.confidence_level = confidence_level
        self.alpha = 1 - confidence_level
        
        # Test configurations
        self.test_config = {
            'min_sample_size': 30,
            'bootstrap_iterations': 1000,
            'monte_carlo_simulations': 10000,
            'outlier_threshold': 3.0,  # Standard deviations
            'normality_alpha': 0.05
        }
        
        logger.info(f"📊 Statistical Validator initialized")
        logger.info(f"🎯 Confidence level: {confidence_level:.1%}")
        logger.info(f"⚠️ Significance level (α): {self.alpha:.3f}")
    
    def test_strategy_significance(self, baseline_returns: List[float], 
                                 strategy_returns: List[float]) -> Dict:
        """
        Test statistical significance of strategy vs baseline
        
        Args:
            baseline_returns: List of baseline strategy returns
            strategy_returns: List of strategy returns (same periods)
            
        Returns:
            Dictionary with test results
        """
        
        logger.info(f"🧪 Testing strategy significance with {len(baseline_returns)} samples")
        
        # Validate inputs
        if len(baseline_returns) != len(strategy_returns):
            raise ValueError("Baseline and strategy returns must have same length")
        
        if len(baseline_returns) < self.test_config['min_sample_size']:
            logger.warning(f"⚠️ Sample size {len(baseline_returns)} below recommended minimum {self.test_config['min_sample_size']}")
        
        # Convert to numpy arrays
        baseline = np.array(baseline_returns)
        strategy = np.array(strategy_returns)
        differences = strategy - baseline
        
        # Basic statistics
        basic_stats = self._calculate_basic_statistics(baseline, strategy, differences)
        
        # Normality tests
        normality_results = self._test_normality(differences)
        
        # Significance tests
        significance_tests = self._run_significance_tests(baseline, strategy, differences, normality_results)
        
        # Effect size analysis
        effect_size = self._calculate_effect_size(baseline, strategy, differences)
        
        # Confidence intervals
        confidence_intervals = self._calculate_confidence_intervals(baseline, strategy, differences)
        
        # Bootstrap analysis
        bootstrap_results = self._bootstrap_analysis(baseline, strategy)
        
        # Robustness tests
        robustness_tests = self._test_robustness(baseline, strategy)
        
        # Overall assessment
        overall_assessment = self._assess_overall_significance(
            significance_tests, effect_size, confidence_intervals, bootstrap_results
        )
        
        return {
            'test_type': 'strategy_significance',
            'sample_size': len(baseline_returns),
            'confidence_level': self.confidence_level,
            'basic_statistics': basic_stats,
            'normality_tests': normality_results,
            'significance_tests': significance_tests,
            'effect_size': effect_size,
            'confidence_intervals': confidence_intervals,
            'bootstrap_results': bootstrap_results,
            'robustness_tests': robustness_tests,
            'overall_assessment': overall_assessment,
            'timestamp': datetime.now().isoformat()
        }
    
    def _calculate_basic_statistics(self, baseline: np.ndarray, 
                                   strategy: np.ndarray, differences: np.ndarray) -> Dict:
        """Calculate basic descriptive statistics"""
        
        return {
            'baseline': {
                'mean': float(np.mean(baseline)),
                'std': float(np.std(baseline, ddof=1)),
                'median': float(np.median(baseline)),
                'min': float(np.min(baseline)),
                'max': float(np.max(baseline)),
                'skewness': float(stats.skew(baseline)),
                'kurtosis': float(stats.kurtosis(baseline))
            },
            'strategy': {
                'mean': float(np.mean(strategy)),
                'std': float(np.std(strategy, ddof=1)),
                'median': float(np.median(strategy)),
                'min': float(np.min(strategy)),
                'max': float(np.max(strategy)),
                'skewness': float(stats.skew(strategy)),
                'kurtosis': float(stats.kurtosis(strategy))
            },
            'differences': {
                'mean': float(np.mean(differences)),
                'std': float(np.std(differences, ddof=1)),
                'median': float(np.median(differences)),
                'positive_periods': int(np.sum(differences > 0)),
                'negative_periods': int(np.sum(differences < 0)),
                'zero_periods': int(np.sum(differences == 0)),
                'improvement_rate': float(np.mean(differences > 0))
            }
        }
    
    def _test_normality(self, data: np.ndarray) -> Dict:
        """Test normality of data distribution"""
        
        # Shapiro-Wilk test (best for small samples)
        if len(data) <= 5000:
            shapiro_stat, shapiro_p = stats.shapiro(data)
        else:
            shapiro_stat, shapiro_p = None, None
        
        # Jarque-Bera test
        jb_stat, jb_p = jarque_bera(data)
        
        # D'Agostino's normality test
        dagostino_stat, dagostino_p = normaltest(data)
        
        # Anderson-Darling test
        ad_result = stats.anderson(data, dist='norm')
        
        return {
            'shapiro_wilk': {
                'statistic': float(shapiro_stat) if shapiro_stat is not None else None,
                'p_value': float(shapiro_p) if shapiro_p is not None else None,
                'is_normal': bool(shapiro_p > self.test_config['normality_alpha']) if shapiro_p is not None else None
            },
            'jarque_bera': {
                'statistic': float(jb_stat),
                'p_value': float(jb_p),
                'is_normal': bool(jb_p > self.test_config['normality_alpha'])
            },
            'dagostino': {
                'statistic': float(dagostino_stat),
                'p_value': float(dagostino_p),
                'is_normal': bool(dagostino_p > self.test_config['normality_alpha'])
            },
            'anderson_darling': {
                'statistic': float(ad_result.statistic),
                'critical_values': ad_result.critical_values.tolist(),
                'significance_levels': ad_result.significance_level.tolist()
            }
        }
    
    def _run_significance_tests(self, baseline: np.ndarray, strategy: np.ndarray, 
                               differences: np.ndarray, normality_results: Dict) -> Dict:
        """Run various significance tests"""
        
        # Paired t-test (parametric)
        t_stat, t_p = ttest_rel(strategy, baseline)
        
        # Wilcoxon signed-rank test (non-parametric)
        try:
            w_stat, w_p = wilcoxon(strategy, baseline, alternative='two-sided')
        except ValueError:
            # Handle case where all differences are zero
            w_stat, w_p = 0, 1.0
        
        # Sign test (non-parametric)
        positive_diffs = np.sum(differences > 0)
        total_non_zero = np.sum(differences != 0)
        if total_non_zero > 0:
            sign_p = 2 * stats.binom.cdf(min(positive_diffs, total_non_zero - positive_diffs), total_non_zero, 0.5)
        else:
            sign_p = 1.0
        
        # Mann-Whitney U test (alternative approach)
        try:
            u_stat, u_p = stats.mannwhitneyu(strategy, baseline, alternative='two-sided')
        except ValueError:
            u_stat, u_p = 0, 1.0
        
        return {
            'paired_t_test': {
                'statistic': float(t_stat),
                'p_value': float(t_p),
                'significant': bool(t_p < self.alpha),
                'test_type': 'parametric',
                'assumptions': 'normality_required'
            },
            'wilcoxon_signed_rank': {
                'statistic': float(w_stat),
                'p_value': float(w_p),
                'significant': bool(w_p < self.alpha),
                'test_type': 'non_parametric',
                'assumptions': 'none'
            },
            'sign_test': {
                'positive_differences': int(positive_diffs),
                'total_non_zero': int(total_non_zero),
                'p_value': float(sign_p),
                'significant': bool(sign_p < self.alpha),
                'test_type': 'non_parametric',
                'assumptions': 'none'
            },
            'mann_whitney_u': {
                'statistic': float(u_stat),
                'p_value': float(u_p),
                'significant': bool(u_p < self.alpha),
                'test_type': 'non_parametric',
                'assumptions': 'independence'
            }
        }
    
    def _calculate_effect_size(self, baseline: np.ndarray, strategy: np.ndarray, 
                              differences: np.ndarray) -> Dict:
        """Calculate effect size measures"""
        
        # Cohen's d
        pooled_std = np.sqrt(((len(baseline) - 1) * np.var(baseline, ddof=1) + 
                             (len(strategy) - 1) * np.var(strategy, ddof=1)) / 
                            (len(baseline) + len(strategy) - 2))
        
        cohens_d = (np.mean(strategy) - np.mean(baseline)) / pooled_std if pooled_std > 0 else 0
        
        # Hedge's g (bias-corrected Cohen's d)
        correction_factor = 1 - (3 / (4 * (len(baseline) + len(strategy)) - 9))
        hedges_g = cohens_d * correction_factor
        
        # Glass's delta
        glass_delta = (np.mean(strategy) - np.mean(baseline)) / np.std(baseline, ddof=1) if np.std(baseline, ddof=1) > 0 else 0
        
        # Probability of superiority
        prob_superiority = np.mean(strategy[:, None] > baseline[None, :])
        
        return {
            'cohens_d': float(cohens_d),
            'hedges_g': float(hedges_g),
            'glass_delta': float(glass_delta),
            'probability_of_superiority': float(prob_superiority),
            'effect_size_interpretation': self._interpret_effect_size(abs(cohens_d))
        }
    
    def _interpret_effect_size(self, effect_size: float) -> str:
        """Interpret effect size magnitude"""
        if effect_size < 0.2:
            return "negligible"
        elif effect_size < 0.5:
            return "small"
        elif effect_size < 0.8:
            return "medium"
        else:
            return "large"
    
    def _calculate_confidence_intervals(self, baseline: np.ndarray, strategy: np.ndarray, 
                                      differences: np.ndarray) -> Dict:
        """Calculate confidence intervals"""
        
        # Confidence interval for mean difference
        mean_diff = np.mean(differences)
        std_diff = np.std(differences, ddof=1)
        se_diff = std_diff / np.sqrt(len(differences))
        
        t_critical = stats.t.ppf(1 - self.alpha/2, len(differences) - 1)
        ci_lower = mean_diff - t_critical * se_diff
        ci_upper = mean_diff + t_critical * se_diff
        
        # Bootstrap confidence interval
        bootstrap_means = []
        for _ in range(self.test_config['bootstrap_iterations']):
            indices = np.random.choice(len(differences), len(differences), replace=True)
            bootstrap_sample = differences[indices]
            bootstrap_means.append(np.mean(bootstrap_sample))
        
        bootstrap_ci_lower = np.percentile(bootstrap_means, 100 * self.alpha/2)
        bootstrap_ci_upper = np.percentile(bootstrap_means, 100 * (1 - self.alpha/2))
        
        return {
            'mean_difference': {
                'point_estimate': float(mean_diff),
                'confidence_interval': [float(ci_lower), float(ci_upper)],
                'method': 'parametric_t'
            },
            'bootstrap_mean_difference': {
                'point_estimate': float(np.mean(bootstrap_means)),
                'confidence_interval': [float(bootstrap_ci_lower), float(bootstrap_ci_upper)],
                'method': 'bootstrap',
                'iterations': self.test_config['bootstrap_iterations']
            }
        }
    
    def _bootstrap_analysis(self, baseline: np.ndarray, strategy: np.ndarray) -> Dict:
        """Perform bootstrap analysis"""
        
        n_bootstrap = self.test_config['bootstrap_iterations']
        bootstrap_differences = []
        
        for _ in range(n_bootstrap):
            # Resample with replacement
            indices = np.random.choice(len(baseline), len(baseline), replace=True)
            boot_baseline = baseline[indices]
            boot_strategy = strategy[indices]
            
            # Calculate difference in means
            diff = np.mean(boot_strategy) - np.mean(boot_baseline)
            bootstrap_differences.append(diff)
        
        bootstrap_differences = np.array(bootstrap_differences)
        
        # Calculate p-value (proportion of bootstrap samples where difference <= 0)
        bootstrap_p_value = np.mean(bootstrap_differences <= 0) * 2  # Two-tailed
        
        return {
            'bootstrap_mean_difference': float(np.mean(bootstrap_differences)),
            'bootstrap_std': float(np.std(bootstrap_differences)),
            'bootstrap_p_value': float(bootstrap_p_value),
            'significant': bool(bootstrap_p_value < self.alpha),
            'iterations': n_bootstrap,
            'positive_improvements': int(np.sum(bootstrap_differences > 0)),
            'improvement_probability': float(np.mean(bootstrap_differences > 0))
        }
    
    def _test_robustness(self, baseline: np.ndarray, strategy: np.ndarray) -> Dict:
        """Test robustness of results"""
        
        # Outlier detection and removal
        differences = strategy - baseline
        z_scores = np.abs(stats.zscore(differences))
        outlier_mask = z_scores > self.test_config['outlier_threshold']
        
        if np.any(outlier_mask):
            # Test without outliers
            clean_baseline = baseline[~outlier_mask]
            clean_strategy = strategy[~outlier_mask]
            
            if len(clean_baseline) > 10:  # Minimum sample size
                clean_t_stat, clean_t_p = ttest_rel(clean_strategy, clean_baseline)
                outlier_robust = {
                    'outliers_detected': int(np.sum(outlier_mask)),
                    'clean_sample_size': len(clean_baseline),
                    'clean_t_statistic': float(clean_t_stat),
                    'clean_p_value': float(clean_t_p),
                    'still_significant': bool(clean_t_p < self.alpha)
                }
            else:
                outlier_robust = {
                    'outliers_detected': int(np.sum(outlier_mask)),
                    'clean_sample_size': len(clean_baseline),
                    'note': 'insufficient_data_after_outlier_removal'
                }
        else:
            outlier_robust = {
                'outliers_detected': 0,
                'note': 'no_outliers_detected'
            }
        
        # Subsample analysis
        subsample_results = []
        if len(baseline) >= 50:  # Only if we have enough data
            for subsample_size in [0.5, 0.7, 0.9]:
                n_subsample = int(len(baseline) * subsample_size)
                subsample_p_values = []
                
                for _ in range(100):  # 100 random subsamples
                    indices = np.random.choice(len(baseline), n_subsample, replace=False)
                    sub_baseline = baseline[indices]
                    sub_strategy = strategy[indices]
                    
                    try:
                        _, p_val = ttest_rel(sub_strategy, sub_baseline)
                        subsample_p_values.append(p_val)
                    except:
                        continue
                
                if subsample_p_values:
                    subsample_results.append({
                        'subsample_fraction': subsample_size,
                        'mean_p_value': float(np.mean(subsample_p_values)),
                        'significant_fraction': float(np.mean(np.array(subsample_p_values) < self.alpha))
                    })
        
        return {
            'outlier_analysis': outlier_robust,
            'subsample_analysis': subsample_results
        }
    
    def _assess_overall_significance(self, significance_tests: Dict, effect_size: Dict, 
                                   confidence_intervals: Dict, bootstrap_results: Dict) -> Dict:
        """Provide overall assessment of statistical significance"""
        
        # Count significant tests
        significant_tests = []
        for test_name, test_result in significance_tests.items():
            if test_result.get('significant', False):
                significant_tests.append(test_name)
        
        # Overall conclusion
        if len(significant_tests) >= 2:  # At least 2 tests significant
            if effect_size['effect_size_interpretation'] in ['medium', 'large']:
                conclusion = "strong_evidence"
            else:
                conclusion = "moderate_evidence"
        elif len(significant_tests) == 1:
            conclusion = "weak_evidence"
        else:
            conclusion = "no_evidence"
        
        # Confidence in results
        ci_excludes_zero = (confidence_intervals['mean_difference']['confidence_interval'][0] > 0 or 
                           confidence_intervals['mean_difference']['confidence_interval'][1] < 0)
        
        return {
            'conclusion': conclusion,
            'significant_tests': significant_tests,
            'total_tests': len(significance_tests),
            'effect_size_magnitude': effect_size['effect_size_interpretation'],
            'confidence_interval_excludes_zero': ci_excludes_zero,
            'bootstrap_supports_significance': bootstrap_results['significant'],
            'improvement_probability': bootstrap_results['improvement_probability'],
            'recommendation': self._generate_recommendation(conclusion, effect_size, ci_excludes_zero)
        }
    
    def _generate_recommendation(self, conclusion: str, effect_size: Dict, ci_excludes_zero: bool) -> str:
        """Generate recommendation based on analysis"""
        
        if conclusion == "strong_evidence":
            return "Strategy shows statistically significant improvement with meaningful effect size. Recommended for implementation."
        elif conclusion == "moderate_evidence":
            return "Strategy shows statistical significance but with small effect size. Consider additional validation."
        elif conclusion == "weak_evidence":
            return "Limited evidence of improvement. Requires more data or different approach."
        else:
            return "No statistical evidence of improvement. Strategy not recommended."

def main():
    """Test statistical validator"""
    
    # Create sample data
    np.random.seed(42)
    baseline_returns = np.random.normal(0.5, 2.0, 100)  # 0.5% mean return, 2% std
    strategy_returns = baseline_returns + np.random.normal(0.3, 0.5, 100)  # Small improvement
    
    validator = StatisticalValidator()
    
    print("🧪 Testing Statistical Validator...")
    
    results = validator.test_strategy_significance(baseline_returns.tolist(), strategy_returns.tolist())
    
    print(f"✅ Analysis completed")
    print(f"📊 Sample size: {results['sample_size']}")
    print(f"🎯 Conclusion: {results['overall_assessment']['conclusion']}")
    print(f"📈 Effect size: {results['effect_size']['effect_size_interpretation']}")
    print(f"💡 Recommendation: {results['overall_assessment']['recommendation']}")

if __name__ == "__main__":
    main()
