#!/usr/bin/env python3
"""
PERFORMANCE COMPARATOR - BENCHMARK COMPARISON SYSTEM
===================================================
Compares trading strategy performance against multiple benchmarks
and baseline strategies with comprehensive statistical analysis.
"""

import numpy as np
import pandas as pd
from scipy import stats
import json
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
import sys
import os
from pathlib import Path

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backtesting'))
sys.path.append(os.path.dirname(__file__))

from statistical_validator import StatisticalValidator
from risk_analyzer import RiskAnalyzer

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class PerformanceComparator:
    """
    Comprehensive performance comparison system
    """
    
    def __init__(self):
        self.statistical_validator = StatisticalValidator()
        self.risk_analyzer = RiskAnalyzer()
        
        # Comparison configuration
        self.config = {
            'benchmark_strategies': [
                'buy_and_hold',
                'simple_grid',
                'moving_average_crossover',
                'rsi_mean_reversion',
                'random_trading'
            ],
            'performance_metrics': [
                'total_return',
                'sharpe_ratio',
                'max_drawdown',
                'win_rate',
                'profit_factor',
                'calmar_ratio'
            ],
            'significance_level': 0.05,
            'min_comparison_periods': 30
        }
        
        logger.info(f"📊 Performance Comparator initialized")
        logger.info(f"🎯 Benchmark strategies: {len(self.config['benchmark_strategies'])}")
    
    def compare_strategies(self, 
                          strategy_results: Dict,
                          benchmark_results: Dict[str, Dict],
                          market_data: Optional[pd.DataFrame] = None) -> Dict:
        """
        Compare strategy performance against multiple benchmarks
        
        Args:
            strategy_results: Results from the strategy being evaluated
            benchmark_results: Dictionary of benchmark strategy results
            market_data: Optional market data for additional analysis
            
        Returns:
            Comprehensive comparison analysis
        """
        
        logger.info(f"📊 Comparing strategy against {len(benchmark_results)} benchmarks")
        
        # Extract performance metrics
        strategy_metrics = self._extract_performance_metrics(strategy_results)
        benchmark_metrics = {name: self._extract_performance_metrics(results) 
                           for name, results in benchmark_results.items()}
        
        # Pairwise comparisons
        pairwise_comparisons = self._perform_pairwise_comparisons(
            strategy_metrics, benchmark_metrics
        )
        
        # Ranking analysis
        ranking_analysis = self._perform_ranking_analysis(
            strategy_metrics, benchmark_metrics
        )
        
        # Risk-adjusted comparisons
        risk_adjusted_comparisons = self._compare_risk_adjusted_performance(
            strategy_metrics, benchmark_metrics
        )
        
        # Statistical significance testing
        significance_tests = self._test_statistical_significance(
            strategy_results, benchmark_results
        )
        
        # Performance attribution
        attribution_analysis = self._analyze_performance_attribution(
            strategy_results, benchmark_results, market_data
        )
        
        # Overall assessment
        overall_assessment = self._assess_overall_performance(
            pairwise_comparisons, ranking_analysis, significance_tests
        )
        
        return {
            'comparison_type': 'comprehensive_strategy_comparison',
            'strategy_name': strategy_results.get('strategy_name', 'evaluated_strategy'),
            'benchmarks_compared': list(benchmark_results.keys()),
            'comparison_period': self._get_comparison_period(strategy_results),
            'strategy_metrics': strategy_metrics,
            'benchmark_metrics': benchmark_metrics,
            'pairwise_comparisons': pairwise_comparisons,
            'ranking_analysis': ranking_analysis,
            'risk_adjusted_comparisons': risk_adjusted_comparisons,
            'significance_tests': significance_tests,
            'attribution_analysis': attribution_analysis,
            'overall_assessment': overall_assessment,
            'timestamp': datetime.now().isoformat()
        }
    
    def _extract_performance_metrics(self, results: Dict) -> Dict:
        """Extract standardized performance metrics from results"""
        
        # Handle different result formats
        if 'performance_summary' in results:
            perf = results['performance_summary']
        elif 'metrics' in results:
            perf = results['metrics']
        else:
            perf = results
        
        return {
            'total_return_pct': perf.get('total_return_pct', 0),
            'annualized_return_pct': perf.get('annualized_return_pct', 0),
            'sharpe_ratio': perf.get('sharpe_ratio', 0),
            'max_drawdown_pct': perf.get('max_drawdown_pct', 0),
            'win_rate': perf.get('win_rate', 0),
            'profit_factor': perf.get('profit_factor', 1),
            'calmar_ratio': perf.get('calmar_ratio', 0),
            'sortino_ratio': perf.get('sortino_ratio', 0),
            'total_trades': perf.get('total_trades', 0),
            'avg_trade_return': perf.get('avg_trade_return', 0),
            'volatility': perf.get('volatility', 0),
            'final_balance': perf.get('final_balance', 0),
            'returns_list': perf.get('returns_list', [])  # For statistical tests
        }
    
    def _perform_pairwise_comparisons(self, strategy_metrics: Dict, 
                                    benchmark_metrics: Dict[str, Dict]) -> Dict:
        """Perform pairwise comparisons between strategy and each benchmark"""
        
        comparisons = {}
        
        for benchmark_name, benchmark_perf in benchmark_metrics.items():
            comparison = {}
            
            # Performance differences
            for metric in self.config['performance_metrics']:
                if metric in strategy_metrics and metric in benchmark_perf:
                    strategy_value = strategy_metrics[metric]
                    benchmark_value = benchmark_perf[metric]
                    
                    if metric == 'max_drawdown_pct':
                        # For drawdown, lower is better
                        improvement = benchmark_value - strategy_value
                        improvement_pct = (improvement / abs(benchmark_value)) * 100 if benchmark_value != 0 else 0
                    else:
                        # For other metrics, higher is better
                        improvement = strategy_value - benchmark_value
                        improvement_pct = (improvement / abs(benchmark_value)) * 100 if benchmark_value != 0 else 0
                    
                    comparison[metric] = {
                        'strategy_value': strategy_value,
                        'benchmark_value': benchmark_value,
                        'absolute_improvement': improvement,
                        'relative_improvement_pct': improvement_pct,
                        'strategy_better': improvement > 0
                    }
            
            # Overall performance score
            comparison['overall_score'] = self._calculate_overall_score(comparison)
            
            comparisons[benchmark_name] = comparison
        
        return comparisons
    
    def _calculate_overall_score(self, comparison: Dict) -> Dict:
        """Calculate overall performance score for comparison"""
        
        # Weight different metrics
        metric_weights = {
            'total_return_pct': 0.25,
            'sharpe_ratio': 0.25,
            'max_drawdown_pct': 0.20,  # Negative weight (lower is better)
            'win_rate': 0.10,
            'profit_factor': 0.10,
            'calmar_ratio': 0.10
        }
        
        weighted_score = 0
        total_weight = 0
        
        for metric, weight in metric_weights.items():
            if metric in comparison:
                improvement_pct = comparison[metric]['relative_improvement_pct']
                
                if metric == 'max_drawdown_pct':
                    # For drawdown, improvement means reduction (positive improvement_pct is good)
                    weighted_score += weight * improvement_pct
                else:
                    weighted_score += weight * improvement_pct
                
                total_weight += weight
        
        if total_weight > 0:
            normalized_score = weighted_score / total_weight
        else:
            normalized_score = 0
        
        # Count metrics where strategy is better
        better_metrics = sum(1 for metric_data in comparison.values() 
                           if isinstance(metric_data, dict) and metric_data.get('strategy_better', False))
        total_metrics = sum(1 for metric_data in comparison.values() 
                          if isinstance(metric_data, dict) and 'strategy_better' in metric_data)
        
        return {
            'weighted_score': normalized_score,
            'metrics_improved': better_metrics,
            'total_metrics': total_metrics,
            'improvement_rate': better_metrics / total_metrics if total_metrics > 0 else 0
        }
    
    def _perform_ranking_analysis(self, strategy_metrics: Dict, 
                                benchmark_metrics: Dict[str, Dict]) -> Dict:
        """Rank strategy against all benchmarks"""
        
        rankings = {}
        
        # Combine all strategies for ranking
        all_strategies = {'evaluated_strategy': strategy_metrics, **benchmark_metrics}
        
        for metric in self.config['performance_metrics']:
            if metric in strategy_metrics:
                # Get values for all strategies
                metric_values = {}
                for name, metrics in all_strategies.items():
                    if metric in metrics:
                        metric_values[name] = metrics[metric]
                
                if len(metric_values) > 1:
                    # Sort strategies by metric value
                    if metric == 'max_drawdown_pct':
                        # For drawdown, lower is better
                        sorted_strategies = sorted(metric_values.items(), key=lambda x: x[1])
                    else:
                        # For other metrics, higher is better
                        sorted_strategies = sorted(metric_values.items(), key=lambda x: x[1], reverse=True)
                    
                    # Find strategy rank
                    strategy_rank = next(i for i, (name, _) in enumerate(sorted_strategies, 1) 
                                       if name == 'evaluated_strategy')
                    
                    rankings[metric] = {
                        'rank': strategy_rank,
                        'total_strategies': len(sorted_strategies),
                        'percentile': (len(sorted_strategies) - strategy_rank + 1) / len(sorted_strategies) * 100,
                        'best_strategy': sorted_strategies[0][0],
                        'worst_strategy': sorted_strategies[-1][0],
                        'strategy_value': metric_values['evaluated_strategy'],
                        'best_value': sorted_strategies[0][1],
                        'worst_value': sorted_strategies[-1][1]
                    }
        
        # Overall ranking (average percentile across metrics)
        if rankings:
            avg_percentile = np.mean([r['percentile'] for r in rankings.values()])
            overall_rank = sum(r['rank'] for r in rankings.values()) / len(rankings)
        else:
            avg_percentile = 0
            overall_rank = 0
        
        return {
            'metric_rankings': rankings,
            'overall_percentile': avg_percentile,
            'average_rank': overall_rank,
            'top_quartile_metrics': sum(1 for r in rankings.values() if r['percentile'] >= 75),
            'bottom_quartile_metrics': sum(1 for r in rankings.values() if r['percentile'] <= 25)
        }
    
    def _compare_risk_adjusted_performance(self, strategy_metrics: Dict, 
                                         benchmark_metrics: Dict[str, Dict]) -> Dict:
        """Compare risk-adjusted performance metrics"""
        
        risk_comparisons = {}
        
        # Risk-adjusted metrics to compare
        risk_metrics = ['sharpe_ratio', 'sortino_ratio', 'calmar_ratio']
        
        for benchmark_name, benchmark_perf in benchmark_metrics.items():
            risk_comparison = {}
            
            for metric in risk_metrics:
                if metric in strategy_metrics and metric in benchmark_perf:
                    strategy_value = strategy_metrics[metric]
                    benchmark_value = benchmark_perf[metric]
                    
                    improvement = strategy_value - benchmark_value
                    improvement_pct = (improvement / abs(benchmark_value)) * 100 if benchmark_value != 0 else 0
                    
                    risk_comparison[metric] = {
                        'strategy_value': strategy_value,
                        'benchmark_value': benchmark_value,
                        'improvement': improvement,
                        'improvement_pct': improvement_pct,
                        'strategy_better': improvement > 0
                    }
            
            # Risk-return efficiency
            strategy_return = strategy_metrics.get('total_return_pct', 0)
            strategy_risk = strategy_metrics.get('max_drawdown_pct', 0)
            benchmark_return = benchmark_perf.get('total_return_pct', 0)
            benchmark_risk = benchmark_perf.get('max_drawdown_pct', 0)
            
            if strategy_risk > 0 and benchmark_risk > 0:
                strategy_efficiency = strategy_return / strategy_risk
                benchmark_efficiency = benchmark_return / benchmark_risk
                efficiency_improvement = strategy_efficiency - benchmark_efficiency
                
                risk_comparison['risk_return_efficiency'] = {
                    'strategy_efficiency': strategy_efficiency,
                    'benchmark_efficiency': benchmark_efficiency,
                    'improvement': efficiency_improvement,
                    'strategy_better': efficiency_improvement > 0
                }
            
            risk_comparisons[benchmark_name] = risk_comparison
        
        return risk_comparisons
    
    def _test_statistical_significance(self, strategy_results: Dict, 
                                     benchmark_results: Dict[str, Dict]) -> Dict:
        """Test statistical significance of performance differences"""
        
        significance_tests = {}
        
        # Extract returns for statistical testing
        strategy_returns = strategy_results.get('returns_list', [])
        if not strategy_returns:
            # Try to extract from other fields
            strategy_returns = strategy_results.get('performance_summary', {}).get('returns_list', [])
        
        if not strategy_returns:
            return {'note': 'strategy_returns_not_available'}
        
        for benchmark_name, benchmark_result in benchmark_results.items():
            benchmark_returns = benchmark_result.get('returns_list', [])
            if not benchmark_returns:
                benchmark_returns = benchmark_result.get('performance_summary', {}).get('returns_list', [])
            
            if benchmark_returns and len(benchmark_returns) == len(strategy_returns):
                try:
                    # Perform statistical significance test
                    test_results = self.statistical_validator.test_strategy_significance(
                        benchmark_returns, strategy_returns
                    )
                    
                    significance_tests[benchmark_name] = {
                        'sample_size': test_results['sample_size'],
                        'significant_tests': test_results['overall_assessment']['significant_tests'],
                        'conclusion': test_results['overall_assessment']['conclusion'],
                        'effect_size': test_results['effect_size']['effect_size_interpretation'],
                        'improvement_probability': test_results['bootstrap_results']['improvement_probability'],
                        'recommendation': test_results['overall_assessment']['recommendation']
                    }
                    
                except Exception as e:
                    significance_tests[benchmark_name] = {'error': str(e)}
            else:
                significance_tests[benchmark_name] = {'note': 'incompatible_return_data'}
        
        return significance_tests
    
    def _analyze_performance_attribution(self, strategy_results: Dict, 
                                       benchmark_results: Dict[str, Dict],
                                       market_data: Optional[pd.DataFrame] = None) -> Dict:
        """Analyze performance attribution"""
        
        attribution = {}
        
        # Basic attribution analysis
        strategy_return = strategy_results.get('performance_summary', {}).get('total_return_pct', 0)
        
        # Compare against buy-and-hold if available
        if 'buy_and_hold' in benchmark_results:
            bnh_return = benchmark_results['buy_and_hold'].get('performance_summary', {}).get('total_return_pct', 0)
            
            attribution['vs_buy_and_hold'] = {
                'strategy_return': strategy_return,
                'buy_and_hold_return': bnh_return,
                'active_return': strategy_return - bnh_return,
                'outperformance': strategy_return > bnh_return
            }
        
        # Market timing vs security selection (simplified)
        if market_data is not None and len(market_data) > 1:
            market_return = ((market_data['close'].iloc[-1] / market_data['close'].iloc[0]) - 1) * 100
            
            attribution['vs_market'] = {
                'strategy_return': strategy_return,
                'market_return': market_return,
                'excess_return': strategy_return - market_return,
                'outperformance': strategy_return > market_return
            }
        
        return attribution
    
    def _assess_overall_performance(self, pairwise_comparisons: Dict, 
                                  ranking_analysis: Dict, 
                                  significance_tests: Dict) -> Dict:
        """Assess overall performance relative to benchmarks"""
        
        # Count wins vs benchmarks
        total_benchmarks = len(pairwise_comparisons)
        wins = sum(1 for comp in pairwise_comparisons.values() 
                  if comp['overall_score']['improvement_rate'] > 0.5)
        
        # Count statistically significant improvements
        significant_improvements = sum(1 for test in significance_tests.values() 
                                     if isinstance(test, dict) and 
                                     test.get('conclusion') in ['strong_evidence', 'moderate_evidence'])
        
        # Overall percentile
        overall_percentile = ranking_analysis.get('overall_percentile', 0)
        
        # Performance classification
        if overall_percentile >= 80 and wins >= total_benchmarks * 0.7:
            performance_class = 'excellent'
        elif overall_percentile >= 60 and wins >= total_benchmarks * 0.5:
            performance_class = 'good'
        elif overall_percentile >= 40:
            performance_class = 'average'
        elif overall_percentile >= 20:
            performance_class = 'below_average'
        else:
            performance_class = 'poor'
        
        return {
            'performance_class': performance_class,
            'overall_percentile': overall_percentile,
            'benchmarks_outperformed': wins,
            'total_benchmarks': total_benchmarks,
            'outperformance_rate': wins / total_benchmarks if total_benchmarks > 0 else 0,
            'statistically_significant_improvements': significant_improvements,
            'top_quartile_performance': overall_percentile >= 75,
            'consistent_outperformance': wins >= total_benchmarks * 0.8,
            'recommendation': self._generate_performance_recommendation(
                performance_class, wins, total_benchmarks, significant_improvements
            )
        }
    
    def _generate_performance_recommendation(self, performance_class: str, 
                                           wins: int, total_benchmarks: int, 
                                           significant_improvements: int) -> str:
        """Generate performance recommendation"""
        
        if performance_class == 'excellent':
            return "Strategy demonstrates excellent performance with consistent outperformance across benchmarks. Recommended for implementation."
        elif performance_class == 'good':
            return "Strategy shows good performance with meaningful improvements over most benchmarks. Consider implementation with appropriate risk management."
        elif performance_class == 'average':
            return "Strategy performance is average. Consider further optimization or additional validation before implementation."
        elif performance_class == 'below_average':
            return "Strategy underperforms most benchmarks. Significant improvements needed before implementation."
        else:
            return "Strategy shows poor performance relative to benchmarks. Not recommended for implementation."
    
    def _get_comparison_period(self, strategy_results: Dict) -> Dict:
        """Extract comparison period information"""
        
        return {
            'start_date': strategy_results.get('start_date', 'unknown'),
            'end_date': strategy_results.get('end_date', 'unknown'),
            'total_periods': strategy_results.get('total_periods', 0),
            'analysis_type': strategy_results.get('analysis_type', 'unknown')
        }

def main():
    """Test performance comparator"""
    
    # Create sample strategy and benchmark results
    strategy_results = {
        'strategy_name': 'test_strategy',
        'performance_summary': {
            'total_return_pct': 15.5,
            'sharpe_ratio': 1.2,
            'max_drawdown_pct': 8.5,
            'win_rate': 0.65,
            'returns_list': np.random.normal(0.1, 1.0, 100).tolist()
        }
    }
    
    benchmark_results = {
        'buy_and_hold': {
            'performance_summary': {
                'total_return_pct': 12.0,
                'sharpe_ratio': 0.8,
                'max_drawdown_pct': 15.0,
                'win_rate': 0.55,
                'returns_list': np.random.normal(0.08, 1.2, 100).tolist()
            }
        },
        'simple_grid': {
            'performance_summary': {
                'total_return_pct': 10.5,
                'sharpe_ratio': 0.9,
                'max_drawdown_pct': 12.0,
                'win_rate': 0.60,
                'returns_list': np.random.normal(0.07, 1.1, 100).tolist()
            }
        }
    }
    
    comparator = PerformanceComparator()
    
    print("🧪 Testing Performance Comparator...")
    
    results = comparator.compare_strategies(strategy_results, benchmark_results)
    
    print(f"✅ Comparison completed")
    print(f"📊 Performance class: {results['overall_assessment']['performance_class']}")
    print(f"🎯 Benchmarks outperformed: {results['overall_assessment']['benchmarks_outperformed']}/{results['overall_assessment']['total_benchmarks']}")
    print(f"📈 Overall percentile: {results['overall_assessment']['overall_percentile']:.1f}%")

if __name__ == "__main__":
    main()
