<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bitcoin Freedom - Enhanced TCN-CNN-PPO</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #1e3c72 0%, #2a5298 100%);
            color: white;
            min-height: 100vh;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            backdrop-filter: blur(10px);
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            background: linear-gradient(45deg, #FFD700, #FFA500);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }
        
        .subtitle {
            font-size: 1.2em;
            opacity: 0.9;
            margin-bottom: 15px;
        }
        
        .performance-banner {
            display: flex;
            justify-content: space-around;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 15px;
        }
        
        .perf-item {
            background: rgba(255, 255, 255, 0.15);
            padding: 10px 15px;
            border-radius: 8px;
            text-align: center;
        }
        
        .perf-value {
            font-size: 1.3em;
            font-weight: bold;
            color: #FFD700;
        }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 15px;
            padding: 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .card h3 {
            margin-bottom: 15px;
            color: #FFD700;
            font-size: 1.3em;
        }
        
        .status-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            padding: 8px 0;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }
        
        .status-value {
            font-weight: bold;
        }
        
        .status-running { color: #4CAF50; }
        .status-stopped { color: #f44336; }
        .status-warning { color: #ff9800; }
        
        .ensemble-weights {
            display: flex;
            justify-content: space-between;
            margin-top: 15px;
        }
        
        .weight-item {
            text-align: center;
            flex: 1;
            padding: 10px;
            background: rgba(255, 255, 255, 0.1);
            margin: 0 5px;
            border-radius: 8px;
        }
        
        .weight-value {
            font-size: 1.5em;
            font-weight: bold;
            color: #FFD700;
        }
        
        .targets-grid {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 10px;
            margin-top: 15px;
        }
        
        .target-item {
            text-align: center;
            padding: 15px;
            border-radius: 8px;
            background: rgba(255, 255, 255, 0.1);
        }
        
        .target-achieved { background: rgba(76, 175, 80, 0.3); }
        .target-missed { background: rgba(244, 67, 54, 0.3); }
        
        .controls {
            text-align: center;
            margin-top: 30px;
        }
        
        .btn {
            background: linear-gradient(45deg, #FFD700, #FFA500);
            color: #1e3c72;
            border: none;
            padding: 12px 25px;
            border-radius: 25px;
            font-size: 1.1em;
            font-weight: bold;
            cursor: pointer;
            margin: 0 10px;
            transition: transform 0.2s;
        }
        
        .btn:hover {
            transform: translateY(-2px);
        }
        
        .btn-stop {
            background: linear-gradient(45deg, #f44336, #d32f2f);
            color: white;
        }
        
        .refresh-info {
            text-align: center;
            margin-top: 20px;
            opacity: 0.7;
        }
        
        @media (max-width: 768px) {
            .performance-banner {
                flex-direction: column;
            }
            
            .ensemble-weights {
                flex-direction: column;
                gap: 10px;
            }
            
            .targets-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🧠 Bitcoin Freedom</h1>
            <div class="subtitle">Enhanced TCN-CNN-PPO Ensemble Trading System</div>
            <div class="performance-banner">
                <div class="perf-item">
                    <div>Win Rate</div>
                    <div class="perf-value">87.3% ✅</div>
                </div>
                <div class="perf-item">
                    <div>Composite Score</div>
                    <div class="perf-value">82.1% ❌</div>
                </div>
                <div class="perf-item">
                    <div>Trades/Day</div>
                    <div class="perf-value">5.0 ✅</div>
                </div>
                <div class="perf-item">
                    <div>Net Profit</div>
                    <div class="perf-value">$3,085</div>
                </div>
                <div class="perf-item">
                    <div>ROI</div>
                    <div class="perf-value">1,028.3%</div>
                </div>
            </div>
        </div>

        <div class="grid">
            <div class="card">
                <h3>🎯 Trading Status</h3>
                <div class="status-item">
                    <span>Engine Status:</span>
                    <span id="engine-status" class="status-value">Loading...</span>
                </div>
                <div class="status-item">
                    <span>Current Price:</span>
                    <span id="current-price" class="status-value">Loading...</span>
                </div>
                <div class="status-item">
                    <span>Account Balance:</span>
                    <span id="account-balance" class="status-value">Loading...</span>
                </div>
                <div class="status-item">
                    <span>Open Trades:</span>
                    <span id="open-trades" class="status-value">Loading...</span>
                </div>
                <div class="status-item">
                    <span>Binance Connection:</span>
                    <span id="binance-status" class="status-value">Loading...</span>
                </div>
            </div>

            <div class="card">
                <h3>🧠 Enhanced TCN-CNN-PPO Ensemble</h3>
                <div class="status-item">
                    <span>Model Type:</span>
                    <span class="status-value">TCN-CNN-PPO</span>
                </div>
                <div class="status-item">
                    <span>Combined Score:</span>
                    <span class="status-value">2.534</span>
                </div>
                <div class="status-item">
                    <span>Total Trades:</span>
                    <span class="status-value">150</span>
                </div>
                
                <div class="ensemble-weights">
                    <div class="weight-item">
                        <div>TCN</div>
                        <div class="weight-value">40%</div>
                    </div>
                    <div class="weight-item">
                        <div>CNN</div>
                        <div class="weight-value">40%</div>
                    </div>
                    <div class="weight-item">
                        <div>PPO</div>
                        <div class="weight-value">20%</div>
                    </div>
                </div>
            </div>

            <div class="card">
                <h3>🎯 Targets Achievement</h3>
                <div class="targets-grid">
                    <div class="target-item target-achieved">
                        <div><strong>Trades/Day</strong></div>
                        <div>5.0 ✅</div>
                        <div><small>Target: 5.0</small></div>
                    </div>
                    <div class="target-item target-achieved">
                        <div><strong>Win Rate</strong></div>
                        <div>87.3% ✅</div>
                        <div><small>Target: >85%</small></div>
                    </div>
                    <div class="target-item target-missed">
                        <div><strong>Composite</strong></div>
                        <div>82.1% ❌</div>
                        <div><small>Target: >90%</small></div>
                    </div>
                </div>
                <div style="text-align: center; margin-top: 15px; font-weight: bold;">
                    🎯 Targets Achieved: 2/3
                </div>
            </div>

            <div class="card">
                <h3>🔍 System Health</h3>
                <div id="health-status">
                    <div class="status-item">
                        <span>Overall Status:</span>
                        <span id="overall-health" class="status-value">Loading...</span>
                    </div>
                    <div class="status-item">
                        <span>Model Validation:</span>
                        <span id="model-health" class="status-value">Loading...</span>
                    </div>
                    <div class="status-item">
                        <span>Ensemble Weights:</span>
                        <span id="weights-health" class="status-value">Loading...</span>
                    </div>
                    <div class="status-item">
                        <span>Database:</span>
                        <span id="database-health" class="status-value">Loading...</span>
                    </div>
                </div>
            </div>
        </div>

        <div class="controls">
            <button class="btn" onclick="startTrading()">🚀 Start Trading</button>
            <button class="btn btn-stop" onclick="stopTrading()">🛑 Stop Trading</button>
            <button class="btn" onclick="refreshData()">🔄 Refresh</button>
        </div>

        <div class="refresh-info">
            <p>🔄 Data refreshes automatically every 30 seconds</p>
            <p>Last updated: <span id="last-update">Loading...</span></p>
        </div>
    </div>

    <script>
        // Auto-refresh data every 30 seconds
        setInterval(refreshData, 30000);
        
        // Initial load
        refreshData();

        async function refreshData() {
            try {
                // Get trading status
                const statusResponse = await fetch('/api/trading_status');
                const status = await statusResponse.json();
                
                // Update trading status
                document.getElementById('engine-status').textContent = status.is_running ? '🟢 Running' : '🔴 Stopped';
                document.getElementById('engine-status').className = status.is_running ? 'status-value status-running' : 'status-value status-stopped';
                document.getElementById('current-price').textContent = `$${status.current_price.toLocaleString()}`;
                document.getElementById('account-balance').textContent = `$${status.account_balance.toFixed(2)}`;
                document.getElementById('open-trades').textContent = status.open_trades;
                document.getElementById('binance-status').textContent = status.is_connected ? '🟢 Connected' : '🟡 Simulation';
                document.getElementById('binance-status').className = status.is_connected ? 'status-value status-running' : 'status-value status-warning';
                
                // Get health status
                const healthResponse = await fetch('/api/health_check');
                const health = await healthResponse.json();
                
                // Update health status
                document.getElementById('overall-health').textContent = health.overall_status;
                document.getElementById('overall-health').className = `status-value ${getHealthClass(health.overall_status)}`;
                document.getElementById('model-health').textContent = health.checks.win_rate || 'Unknown';
                document.getElementById('weights-health').textContent = health.checks.ensemble_weights || 'Unknown';
                document.getElementById('database-health').textContent = health.checks.database || 'Unknown';
                
                // Update timestamp
                document.getElementById('last-update').textContent = new Date().toLocaleTimeString();
                
            } catch (error) {
                console.error('Error refreshing data:', error);
                document.getElementById('last-update').textContent = 'Error loading data';
            }
        }

        function getHealthClass(status) {
            switch(status) {
                case 'HEALTHY': return 'status-running';
                case 'WARNING': return 'status-warning';
                case 'CRITICAL': return 'status-stopped';
                default: return 'status-warning';
            }
        }

        async function startTrading() {
            try {
                const response = await fetch('/api/start_trading', { method: 'POST' });
                const result = await response.json();
                alert(result.message);
                refreshData();
            } catch (error) {
                alert('Error starting trading: ' + error.message);
            }
        }

        async function stopTrading() {
            try {
                const response = await fetch('/api/stop_trading', { method: 'POST' });
                const result = await response.json();
                alert(result.message);
                refreshData();
            } catch (error) {
                alert('Error stopping trading: ' + error.message);
            }
        }
    </script>
</body>
</html>
