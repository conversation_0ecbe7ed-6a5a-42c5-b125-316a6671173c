#!/usr/bin/env python3
"""
REALISTIC TRADE SIMULATOR - REAL MARKET CONDITIONS
=================================================
Simulates actual trading conditions with slippage, commissions, and realistic order execution.
Implements grid limit orders at exact 0.25% levels with proper fill simulation.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, NamedTuple
import random
import logging

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class MarketConditions(NamedTuple):
    """Market conditions for realistic execution"""
    timestamp: datetime
    open: float
    high: float
    low: float
    close: float
    volume: float
    spread: float
    volatility: float

class LimitOrder(NamedTuple):
    """Limit order structure"""
    order_id: str
    side: str  # 'BUY' or 'SELL'
    price: float
    quantity: float
    timestamp: datetime
    grid_level: int
    take_profit_price: float
    stop_loss_price: float
    time_in_force: str = 'GTC'

class TradeExecution(NamedTuple):
    """Trade execution record"""
    order_id: str
    side: str
    quantity: float
    execution_price: float
    commission: float
    slippage: float
    timestamp: datetime
    grid_level: int
    trade_value: float

class RealisticSlippageModel:
    """Models realistic slippage based on market conditions"""
    
    def __init__(self):
        self.base_slippage = 0.0001    # 0.01% base slippage
        self.volume_impact_factor = 0.00001
        self.spread_factor = 0.5
        self.max_slippage = 0.001      # 0.1% maximum slippage
    
    def calculate_slippage(self, order_size_usd: float, market_volume: float, 
                          spread: float, volatility: float) -> float:
        """Calculate realistic slippage based on market conditions"""
        
        # Volume impact: larger orders relative to market volume have more slippage
        volume_impact = (order_size_usd / (market_volume * 50000)) * self.volume_impact_factor
        
        # Spread impact: wider spreads increase slippage
        spread_impact = spread * self.spread_factor
        
        # Volatility impact: higher volatility increases slippage
        volatility_impact = volatility * 0.0001
        
        # Total slippage
        total_slippage = self.base_slippage + volume_impact + spread_impact + volatility_impact
        
        # Cap at maximum slippage
        return min(total_slippage, self.max_slippage)
    
    def apply_slippage(self, order_price: float, order_side: str, 
                      market_conditions: MarketConditions, order_size_usd: float) -> Tuple[float, float]:
        """Apply calculated slippage to order execution"""
        
        slippage = self.calculate_slippage(
            order_size_usd=order_size_usd,
            market_volume=market_conditions.volume,
            spread=market_conditions.spread,
            volatility=market_conditions.volatility
        )
        
        if order_side == 'BUY':
            # Buy orders pay more due to slippage
            execution_price = order_price * (1 + slippage)
        else:  # SELL
            # Sell orders receive less due to slippage
            execution_price = order_price * (1 - slippage)
        
        return execution_price, slippage

class GridLimitOrderSystem:
    """Manages grid limit orders at exact 0.25% levels"""
    
    def __init__(self):
        self.grid_spacing = 0.0025     # 0.25% spacing (LOCKED)
        self.position_size_usd = 10.0  # $10 per trade (LOCKED)
        self.take_profit_pct = 0.0025  # 0.25% take profit
        self.stop_loss_pct = 0.001     # 0.1% stop loss
        self.risk_reward_ratio = 2.5   # 2.5:1 ratio (LOCKED)
        
    def calculate_grid_levels(self, current_price: float, num_levels: int = 10) -> List[Dict]:
        """Calculate grid levels above and below current price"""
        
        grid_levels = []
        
        for i in range(-num_levels, num_levels + 1):
            if i == 0:
                continue  # Skip current price level
            
            level_price = current_price * (1 + (i * self.grid_spacing))
            
            grid_level = {
                'level': i,
                'price': level_price,
                'side': 'BUY' if i < 0 else 'SELL',
                'distance_pct': abs(i * self.grid_spacing * 100),
                'active': False,
                'order_id': None
            }
            
            grid_levels.append(grid_level)
        
        return grid_levels
    
    def create_limit_orders(self, grid_levels: List[Dict], current_time: datetime) -> List[LimitOrder]:
        """Create limit orders for each grid level"""
        
        orders = []
        
        for level in grid_levels:
            # Calculate position quantity for $10 USD
            quantity = self.position_size_usd / level['price']
            
            if level['side'] == 'BUY':
                # Buy limit order below current price
                order = LimitOrder(
                    order_id=f"GRID_BUY_{level['level']}_{int(current_time.timestamp())}",
                    side='BUY',
                    price=level['price'],
                    quantity=quantity,
                    timestamp=current_time,
                    grid_level=level['level'],
                    take_profit_price=level['price'] * (1 + self.take_profit_pct),
                    stop_loss_price=level['price'] * (1 - self.stop_loss_pct)
                )
            else:  # SELL
                # Sell limit order above current price
                order = LimitOrder(
                    order_id=f"GRID_SELL_{level['level']}_{int(current_time.timestamp())}",
                    side='SELL',
                    price=level['price'],
                    quantity=quantity,
                    timestamp=current_time,
                    grid_level=level['level'],
                    take_profit_price=level['price'] * (1 - self.take_profit_pct),
                    stop_loss_price=level['price'] * (1 + self.stop_loss_pct)
                )
            
            orders.append(order)
            level['active'] = True
            level['order_id'] = order.order_id
        
        return orders

class RealisticExecutionEngine:
    """Executes orders with realistic market conditions"""
    
    def __init__(self):
        self.slippage_model = RealisticSlippageModel()
        self.commission_rate = 0.001  # 0.1% commission
        self.partial_fill_probability = 0.1  # 10% chance of partial fill
        self.min_fill_ratio = 0.5     # Minimum 50% fill for partial fills
        
    def check_order_execution(self, order: LimitOrder, market_bar: MarketConditions) -> bool:
        """Check if limit order should execute based on market data"""
        
        # For buy orders: execute if low price touches or goes below limit price
        if order.side == 'BUY' and market_bar.low <= order.price:
            return True
        
        # For sell orders: execute if high price touches or goes above limit price
        if order.side == 'SELL' and market_bar.high >= order.price:
            return True
        
        return False
    
    def simulate_partial_fill(self, order: LimitOrder) -> float:
        """Simulate partial fill based on market conditions"""
        
        if random.random() < self.partial_fill_probability:
            # Partial fill: 50-90% of order quantity
            fill_ratio = random.uniform(self.min_fill_ratio, 0.9)
            return order.quantity * fill_ratio
        else:
            # Full fill
            return order.quantity
    
    def execute_limit_order(self, order: LimitOrder, market_bar: MarketConditions) -> Optional[TradeExecution]:
        """Execute limit order with realistic conditions"""
        
        # 1. Check if order can execute
        if not self.check_order_execution(order, market_bar):
            return None
        
        # 2. Simulate partial fill
        fill_quantity = self.simulate_partial_fill(order)
        
        # 3. Calculate order size in USD
        order_size_usd = fill_quantity * order.price
        
        # 4. Apply realistic slippage
        execution_price, slippage = self.slippage_model.apply_slippage(
            order.price, order.side, market_bar, order_size_usd
        )
        
        # 5. Calculate trade value and commission
        trade_value = fill_quantity * execution_price
        commission = trade_value * self.commission_rate
        
        # 6. Create execution record
        execution = TradeExecution(
            order_id=order.order_id,
            side=order.side,
            quantity=fill_quantity,
            execution_price=execution_price,
            commission=commission,
            slippage=slippage,
            timestamp=market_bar.timestamp,
            grid_level=order.grid_level,
            trade_value=trade_value
        )
        
        logger.info(f"🎯 Order Executed: {order.side} {fill_quantity:.6f} BTC at ${execution_price:,.2f} "
                   f"(Slippage: {slippage:.4%}, Commission: ${commission:.2f})")
        
        return execution

class RealTradeSimulator:
    """Main simulator for realistic trading conditions"""
    
    def __init__(self):
        self.grid_system = GridLimitOrderSystem()
        self.execution_engine = RealisticExecutionEngine()
        self.portfolio_balance = 300.0  # Starting balance
        self.active_orders = []
        self.executed_trades = []
        self.grid_levels = []
        
    def calculate_spread(self, high: float, low: float, close: float, volume: float) -> float:
        """Calculate realistic bid-ask spread"""
        # Estimate spread based on volatility and volume
        volatility = (high - low) / close
        volume_factor = 1 / (volume / 1000000)  # Lower volume = wider spread

        spread = (volatility * 0.001) + (volume_factor * 0.0001)
        return min(spread, 0.002)  # Cap at 0.2%
    
    def should_update_grid(self, current_price: float) -> bool:
        """Determine if grid should be recentered"""
        if not self.grid_levels:
            return True
        
        # Find current grid center
        grid_center = None
        for level in self.grid_levels:
            if level['level'] == 0:
                grid_center = level['price']
                break
        
        if grid_center is None:
            return True
        
        # Recenter if price moved more than 2% from grid center
        price_change = abs(current_price - grid_center) / grid_center
        return price_change > 0.02  # 2% threshold
    
    def update_grid(self, current_price: float, current_time: datetime):
        """Update grid levels and place new orders"""
        
        # Cancel existing orders
        self.active_orders = []
        
        # Create new grid levels
        self.grid_levels = self.grid_system.calculate_grid_levels(current_price)
        
        # Place new limit orders
        new_orders = self.grid_system.create_limit_orders(self.grid_levels, current_time)
        self.active_orders.extend(new_orders)
        
        logger.info(f"🔄 Grid updated: {len(new_orders)} orders placed around ${current_price:,.2f}")
    
    def process_market_bar(self, market_data: Dict) -> List[TradeExecution]:
        """Process one minute of market data"""
        
        # Create market conditions
        market_bar = MarketConditions(
            timestamp=market_data['datetime'],
            open=market_data['open'],
            high=market_data['high'],
            low=market_data['low'],
            close=market_data['close'],
            volume=market_data['volume'],
            spread=self.calculate_spread(
                market_data['high'], market_data['low'],
                market_data['close'], market_data['volume']
            ),
            volatility=(market_data['high'] - market_data['low']) / market_data['close']
        )
        
        # Update grid if needed
        if self.should_update_grid(market_bar.close):
            self.update_grid(market_bar.close, market_bar.timestamp)
        
        # Check for order executions
        executed_orders = []
        minute_executions = []
        
        for order in self.active_orders:
            execution = self.execution_engine.execute_limit_order(order, market_bar)
            
            if execution:
                minute_executions.append(execution)
                executed_orders.append(order)
                self.executed_trades.append(execution)
                
                # Update portfolio balance
                if execution.side == 'BUY':
                    self.portfolio_balance -= (execution.trade_value + execution.commission)
                else:  # SELL
                    self.portfolio_balance += (execution.trade_value - execution.commission)
        
        # Remove executed orders
        for order in executed_orders:
            self.active_orders.remove(order)
        
        return minute_executions
    
    def get_simulation_stats(self) -> Dict:
        """Get current simulation statistics"""
        
        if not self.executed_trades:
            return {
                'total_trades': 0,
                'total_commission': 0.0,
                'total_slippage': 0.0,
                'portfolio_balance': self.portfolio_balance,
                'active_orders': len(self.active_orders),
                'pnl': self.portfolio_balance - 300.0  # Starting balance was $300
            }
        
        total_commission = sum(trade.commission for trade in self.executed_trades)
        total_slippage = sum(abs(trade.slippage) for trade in self.executed_trades)
        avg_slippage = total_slippage / len(self.executed_trades)
        
        pnl = self.portfolio_balance - 300.0  # Starting balance was $300

        return {
            'total_trades': len(self.executed_trades),
            'total_commission': total_commission,
            'total_slippage': total_slippage,
            'avg_slippage_pct': avg_slippage * 100,
            'portfolio_balance': self.portfolio_balance,
            'active_orders': len(self.active_orders),
            'pnl': pnl
        }

def main():
    """Test the realistic trade simulator"""
    simulator = RealTradeSimulator()
    
    # Test with sample market data
    sample_data = {
        'datetime': datetime.now(),
        'open': 50000.0,
        'high': 50100.0,
        'low': 49900.0,
        'close': 50050.0,
        'volume': 1000000.0
    }
    
    executions = simulator.process_market_bar(sample_data)
    stats = simulator.get_simulation_stats()
    
    print("🎯 Realistic Trade Simulator Test")
    print(f"📊 Executions: {len(executions)}")
    print(f"📈 Stats: {stats}")

if __name__ == "__main__":
    main()
