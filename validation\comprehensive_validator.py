#!/usr/bin/env python3
"""
COMPREHENSIVE VALIDATOR - COMPLETE VALIDATION SUITE
==================================================
Integrates all validation components for complete strategy assessment:
- Walk-forward analysis
- Statistical significance testing
- Risk analysis
- Performance comparison
"""

import numpy as np
import pandas as pd
import sqlite3
import json
import argparse
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
import sys
import os
from pathlib import Path

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backtesting'))
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'reinforcement_learning'))
sys.path.append(os.path.dirname(__file__))

from walk_forward_analyzer import WalkForwardAnalyzer
from statistical_validator import StatisticalValidator
from risk_analyzer import RiskAnalyzer
from performance_comparator import PerformanceComparator
from enhanced_backtester import EnhancedBacktester

# Setup logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

class ComprehensiveValidator:
    """
    Complete validation suite for trading strategies
    """
    
    def __init__(self, 
                 db_path: str = "data/bitcoin_historical.db",
                 results_dir: str = "results/validation"):
        
        self.db_path = db_path
        self.results_dir = Path(results_dir)
        self.results_dir.mkdir(parents=True, exist_ok=True)
        
        # Initialize validation components
        self.walk_forward_analyzer = WalkForwardAnalyzer(db_path, str(self.results_dir / "walk_forward"))
        self.statistical_validator = StatisticalValidator()
        self.risk_analyzer = RiskAnalyzer()
        self.performance_comparator = PerformanceComparator()
        self.backtester = EnhancedBacktester()
        
        # Validation configuration
        self.config = {
            'validation_types': [
                'walk_forward',
                'statistical_significance',
                'risk_analysis',
                'performance_comparison'
            ],
            'benchmark_strategies': [
                'buy_and_hold',
                'simple_grid',
                'random_trading'
            ],
            'confidence_level': 0.95,
            'min_validation_periods': 10,
            'risk_tolerance': 'moderate'  # low, moderate, high
        }
        
        logger.info(f"🔬 Comprehensive Validator initialized")
        logger.info(f"📊 Validation types: {len(self.config['validation_types'])}")
        logger.info(f"🎯 Benchmark strategies: {len(self.config['benchmark_strategies'])}")
    
    def run_comprehensive_validation(self, 
                                   strategy_name: str,
                                   start_date: str,
                                   end_date: str,
                                   strategy_config: Optional[Dict] = None) -> Dict:
        """
        Run complete validation suite for a trading strategy
        
        Args:
            strategy_name: Name of the strategy to validate
            start_date: Start date for validation (YYYY-MM-DD)
            end_date: End date for validation (YYYY-MM-DD)
            strategy_config: Optional strategy configuration
            
        Returns:
            Comprehensive validation results
        """
        
        logger.info(f"🔬 Starting comprehensive validation for {strategy_name}")
        logger.info(f"📅 Period: {start_date} to {end_date}")
        
        validation_start_time = datetime.now()
        
        # Load historical data
        historical_data = self._load_historical_data(start_date, end_date)
        if historical_data.empty:
            raise ValueError("No historical data available for validation period")
        
        # Run strategy backtest
        strategy_results = self._run_strategy_backtest(historical_data, strategy_config)
        
        # Generate benchmark results
        benchmark_results = self._generate_benchmark_results(historical_data)
        
        # Validation results container
        validation_results = {
            'strategy_name': strategy_name,
            'validation_period': {
                'start_date': start_date,
                'end_date': end_date,
                'total_days': (pd.to_datetime(end_date) - pd.to_datetime(start_date)).days,
                'data_points': len(historical_data)
            },
            'strategy_results': strategy_results,
            'benchmark_results': benchmark_results
        }
        
        # 1. Walk-Forward Analysis
        if 'walk_forward' in self.config['validation_types']:
            logger.info("🚶 Running walk-forward analysis...")
            try:
                walk_forward_results = self.walk_forward_analyzer.run_walk_forward_analysis(
                    start_date, end_date
                )
                validation_results['walk_forward_analysis'] = walk_forward_results
                logger.info("✅ Walk-forward analysis completed")
            except Exception as e:
                logger.error(f"❌ Walk-forward analysis failed: {e}")
                validation_results['walk_forward_analysis'] = {'error': str(e)}
        
        # 2. Statistical Significance Testing
        if 'statistical_significance' in self.config['validation_types']:
            logger.info("📊 Running statistical significance tests...")
            try:
                significance_results = self._run_statistical_tests(strategy_results, benchmark_results)
                validation_results['statistical_significance'] = significance_results
                logger.info("✅ Statistical significance tests completed")
            except Exception as e:
                logger.error(f"❌ Statistical significance tests failed: {e}")
                validation_results['statistical_significance'] = {'error': str(e)}
        
        # 3. Risk Analysis
        if 'risk_analysis' in self.config['validation_types']:
            logger.info("⚠️ Running risk analysis...")
            try:
                risk_results = self._run_risk_analysis(strategy_results, historical_data)
                validation_results['risk_analysis'] = risk_results
                logger.info("✅ Risk analysis completed")
            except Exception as e:
                logger.error(f"❌ Risk analysis failed: {e}")
                validation_results['risk_analysis'] = {'error': str(e)}
        
        # 4. Performance Comparison
        if 'performance_comparison' in self.config['validation_types']:
            logger.info("🏆 Running performance comparison...")
            try:
                comparison_results = self.performance_comparator.compare_strategies(
                    strategy_results, benchmark_results, historical_data
                )
                validation_results['performance_comparison'] = comparison_results
                logger.info("✅ Performance comparison completed")
            except Exception as e:
                logger.error(f"❌ Performance comparison failed: {e}")
                validation_results['performance_comparison'] = {'error': str(e)}
        
        # Generate overall assessment
        overall_assessment = self._generate_overall_assessment(validation_results)
        validation_results['overall_assessment'] = overall_assessment
        
        # Calculate validation duration
        validation_duration = datetime.now() - validation_start_time
        validation_results['validation_metadata'] = {
            'validation_duration_seconds': validation_duration.total_seconds(),
            'validation_timestamp': datetime.now().isoformat(),
            'validator_version': '1.0.0',
            'configuration': self.config
        }
        
        # Save results
        self._save_validation_results(validation_results)
        
        logger.info(f"✅ Comprehensive validation completed in {validation_duration}")
        return validation_results
    
    def _load_historical_data(self, start_date: str, end_date: str) -> pd.DataFrame:
        """Load historical data for validation period"""
        
        try:
            conn = sqlite3.connect(self.db_path)
            
            query = """
                SELECT timestamp, datetime, open, high, low, close, volume,
                       estimated_spread, volatility, volume_ratio
                FROM ohlcv_data 
                WHERE datetime BETWEEN ? AND ?
                ORDER BY timestamp
            """
            
            df = pd.read_sql_query(query, conn, params=(start_date, end_date))
            conn.close()
            
            if df.empty:
                logger.warning(f"⚠️ No data found for period {start_date} to {end_date}")
                return pd.DataFrame()
            
            # Convert datetime column
            df['datetime'] = pd.to_datetime(df['datetime'])
            
            logger.info(f"📊 Loaded {len(df):,} records for validation")
            return df
            
        except Exception as e:
            logger.error(f"❌ Error loading historical data: {e}")
            return pd.DataFrame()
    
    def _run_strategy_backtest(self, historical_data: pd.DataFrame, 
                              strategy_config: Optional[Dict] = None) -> Dict:
        """Run backtest for the strategy being validated"""
        
        try:
            # Run enhanced backtesting
            results = self.backtester.run_backtest(
                historical_data=historical_data,
                start_balance=300.0
            )
            
            # Extract returns for statistical analysis
            if 'trade_history' in results:
                returns = self._extract_returns_from_trades(results['trade_history'])
                results['returns_list'] = returns
            
            return results
            
        except Exception as e:
            logger.error(f"❌ Strategy backtest failed: {e}")
            return {'error': str(e)}
    
    def _generate_benchmark_results(self, historical_data: pd.DataFrame) -> Dict[str, Dict]:
        """Generate results for benchmark strategies"""
        
        benchmark_results = {}
        
        # Buy and Hold
        if 'buy_and_hold' in self.config['benchmark_strategies']:
            try:
                bnh_results = self._run_buy_and_hold(historical_data)
                benchmark_results['buy_and_hold'] = bnh_results
            except Exception as e:
                logger.error(f"❌ Buy and hold benchmark failed: {e}")
                benchmark_results['buy_and_hold'] = {'error': str(e)}
        
        # Simple Grid (baseline)
        if 'simple_grid' in self.config['benchmark_strategies']:
            try:
                grid_results = self.backtester.run_backtest(
                    historical_data=historical_data,
                    start_balance=300.0
                )
                if 'trade_history' in grid_results:
                    returns = self._extract_returns_from_trades(grid_results['trade_history'])
                    grid_results['returns_list'] = returns
                benchmark_results['simple_grid'] = grid_results
            except Exception as e:
                logger.error(f"❌ Simple grid benchmark failed: {e}")
                benchmark_results['simple_grid'] = {'error': str(e)}
        
        # Random Trading
        if 'random_trading' in self.config['benchmark_strategies']:
            try:
                random_results = self._run_random_trading(historical_data)
                benchmark_results['random_trading'] = random_results
            except Exception as e:
                logger.error(f"❌ Random trading benchmark failed: {e}")
                benchmark_results['random_trading'] = {'error': str(e)}
        
        return benchmark_results
    
    def _run_buy_and_hold(self, historical_data: pd.DataFrame) -> Dict:
        """Run buy and hold benchmark"""
        
        if len(historical_data) < 2:
            return {'error': 'insufficient_data'}
        
        start_price = historical_data['close'].iloc[0]
        end_price = historical_data['close'].iloc[-1]
        
        total_return = (end_price - start_price) / start_price
        total_return_pct = total_return * 100
        
        # Calculate daily returns
        daily_returns = historical_data['close'].pct_change().dropna()
        returns_list = (daily_returns * 100).tolist()  # Convert to percentage
        
        # Calculate other metrics
        volatility = daily_returns.std() * np.sqrt(252) * 100  # Annualized volatility
        
        # Calculate drawdowns
        cumulative = (1 + daily_returns).cumprod()
        running_max = cumulative.expanding().max()
        drawdowns = (cumulative - running_max) / running_max
        max_drawdown_pct = abs(drawdowns.min()) * 100
        
        # Sharpe ratio (simplified)
        risk_free_rate = 0.02 / 252  # Daily risk-free rate
        excess_returns = daily_returns - risk_free_rate
        sharpe_ratio = excess_returns.mean() / daily_returns.std() * np.sqrt(252) if daily_returns.std() > 0 else 0
        
        return {
            'strategy_name': 'buy_and_hold',
            'total_return_pct': total_return_pct,
            'max_drawdown_pct': max_drawdown_pct,
            'sharpe_ratio': sharpe_ratio,
            'volatility': volatility,
            'total_trades': 1,  # One buy transaction
            'win_rate': 1.0 if total_return > 0 else 0.0,
            'final_balance': 300 * (1 + total_return),
            'returns_list': returns_list
        }
    
    def _run_random_trading(self, historical_data: pd.DataFrame) -> Dict:
        """Run random trading benchmark"""
        
        np.random.seed(42)  # For reproducibility
        
        # Simulate random trades
        num_trades = min(50, len(historical_data) // 20)  # One trade per 20 periods
        trade_returns = np.random.normal(0, 0.02, num_trades)  # 2% std random returns
        
        total_return = np.prod(1 + trade_returns) - 1
        total_return_pct = total_return * 100
        
        # Calculate metrics
        returns_list = (trade_returns * 100).tolist()
        max_drawdown_pct = abs(np.min(np.minimum.accumulate(np.cumsum(trade_returns)))) * 100
        sharpe_ratio = np.mean(trade_returns) / np.std(trade_returns) * np.sqrt(252) if np.std(trade_returns) > 0 else 0
        win_rate = np.mean(trade_returns > 0)
        
        return {
            'strategy_name': 'random_trading',
            'total_return_pct': total_return_pct,
            'max_drawdown_pct': max_drawdown_pct,
            'sharpe_ratio': sharpe_ratio,
            'total_trades': num_trades,
            'win_rate': win_rate,
            'final_balance': 300 * (1 + total_return),
            'returns_list': returns_list
        }
    
    def _extract_returns_from_trades(self, trade_history: List[Dict]) -> List[float]:
        """Extract returns from trade history"""
        
        returns = []
        for trade in trade_history:
            if 'pnl_pct' in trade:
                returns.append(trade['pnl_pct'])
            elif 'pnl' in trade and 'size' in trade and trade['size'] > 0:
                return_pct = (trade['pnl'] / trade['size']) * 100
                returns.append(return_pct)
        
        return returns
    
    def _run_statistical_tests(self, strategy_results: Dict, 
                              benchmark_results: Dict[str, Dict]) -> Dict:
        """Run statistical significance tests"""
        
        significance_results = {}
        
        strategy_returns = strategy_results.get('returns_list', [])
        if not strategy_returns:
            return {'error': 'no_strategy_returns'}
        
        for benchmark_name, benchmark_result in benchmark_results.items():
            if 'error' in benchmark_result:
                continue
                
            benchmark_returns = benchmark_result.get('returns_list', [])
            if benchmark_returns and len(benchmark_returns) >= len(strategy_returns) * 0.8:
                # Align lengths
                min_length = min(len(strategy_returns), len(benchmark_returns))
                strategy_subset = strategy_returns[:min_length]
                benchmark_subset = benchmark_returns[:min_length]
                
                try:
                    test_results = self.statistical_validator.test_strategy_significance(
                        benchmark_subset, strategy_subset
                    )
                    significance_results[benchmark_name] = test_results
                except Exception as e:
                    significance_results[benchmark_name] = {'error': str(e)}
        
        return significance_results
    
    def _run_risk_analysis(self, strategy_results: Dict, 
                          historical_data: pd.DataFrame) -> Dict:
        """Run comprehensive risk analysis"""
        
        strategy_returns = strategy_results.get('returns_list', [])
        if not strategy_returns:
            return {'error': 'no_strategy_returns'}
        
        # Extract prices for additional analysis
        prices = historical_data['close'].tolist() if not historical_data.empty else None
        
        try:
            risk_results = self.risk_analyzer.analyze_strategy_risk(
                returns=strategy_returns,
                prices=prices
            )
            return risk_results
        except Exception as e:
            return {'error': str(e)}
    
    def _generate_overall_assessment(self, validation_results: Dict) -> Dict:
        """Generate overall assessment from all validation components"""
        
        assessment = {
            'validation_score': 0,
            'validation_grade': 'F',
            'key_findings': [],
            'recommendations': [],
            'risk_level': 'unknown',
            'implementation_ready': False
        }
        
        score = 0
        max_score = 0
        
        # Walk-forward analysis scoring
        if 'walk_forward_analysis' in validation_results and 'error' not in validation_results['walk_forward_analysis']:
            wf_results = validation_results['walk_forward_analysis']
            if 'aggregate_metrics' in wf_results:
                improvement_rate = wf_results['aggregate_metrics'].get('improvement_analysis', {}).get('improvement_rate', 0)
                score += improvement_rate * 25  # Max 25 points
                assessment['key_findings'].append(f"Walk-forward improvement rate: {improvement_rate:.1%}")
            max_score += 25
        
        # Statistical significance scoring
        if 'statistical_significance' in validation_results:
            sig_results = validation_results['statistical_significance']
            significant_tests = 0
            total_tests = 0
            
            for benchmark_test in sig_results.values():
                if isinstance(benchmark_test, dict) and 'overall_assessment' in benchmark_test:
                    total_tests += 1
                    if benchmark_test['overall_assessment'].get('conclusion') in ['strong_evidence', 'moderate_evidence']:
                        significant_tests += 1
            
            if total_tests > 0:
                sig_rate = significant_tests / total_tests
                score += sig_rate * 25  # Max 25 points
                assessment['key_findings'].append(f"Statistical significance rate: {sig_rate:.1%}")
            max_score += 25
        
        # Performance comparison scoring
        if 'performance_comparison' in validation_results and 'error' not in validation_results['performance_comparison']:
            perf_results = validation_results['performance_comparison']
            if 'overall_assessment' in perf_results:
                overall_perf = perf_results['overall_assessment']
                percentile = overall_perf.get('overall_percentile', 0)
                score += (percentile / 100) * 25  # Max 25 points
                assessment['key_findings'].append(f"Performance percentile: {percentile:.1f}%")
            max_score += 25
        
        # Risk analysis scoring
        if 'risk_analysis' in validation_results and 'error' not in validation_results['risk_analysis']:
            risk_results = validation_results['risk_analysis']
            if 'overall_assessment' in risk_results:
                risk_level = risk_results['overall_assessment'].get('risk_level', 'unknown')
                assessment['risk_level'] = risk_level
                
                # Risk scoring (lower risk = higher score)
                risk_scores = {'very_low': 25, 'low': 20, 'moderate': 15, 'high': 10, 'very_high': 5}
                score += risk_scores.get(risk_level, 0)
                assessment['key_findings'].append(f"Risk level: {risk_level}")
            max_score += 25
        
        # Calculate final score and grade
        if max_score > 0:
            assessment['validation_score'] = (score / max_score) * 100
            
            if assessment['validation_score'] >= 90:
                assessment['validation_grade'] = 'A+'
            elif assessment['validation_score'] >= 85:
                assessment['validation_grade'] = 'A'
            elif assessment['validation_score'] >= 80:
                assessment['validation_grade'] = 'A-'
            elif assessment['validation_score'] >= 75:
                assessment['validation_grade'] = 'B+'
            elif assessment['validation_score'] >= 70:
                assessment['validation_grade'] = 'B'
            elif assessment['validation_score'] >= 65:
                assessment['validation_grade'] = 'B-'
            elif assessment['validation_score'] >= 60:
                assessment['validation_grade'] = 'C+'
            elif assessment['validation_score'] >= 55:
                assessment['validation_grade'] = 'C'
            elif assessment['validation_score'] >= 50:
                assessment['validation_grade'] = 'C-'
            elif assessment['validation_score'] >= 40:
                assessment['validation_grade'] = 'D'
            else:
                assessment['validation_grade'] = 'F'
        
        # Implementation readiness
        assessment['implementation_ready'] = (
            assessment['validation_score'] >= 70 and 
            assessment['risk_level'] in ['very_low', 'low', 'moderate']
        )
        
        # Generate recommendations
        if assessment['validation_score'] >= 80:
            assessment['recommendations'].append("Strategy shows strong validation results. Recommended for implementation.")
        elif assessment['validation_score'] >= 60:
            assessment['recommendations'].append("Strategy shows moderate validation results. Consider additional testing.")
        else:
            assessment['recommendations'].append("Strategy requires significant improvement before implementation.")
        
        if assessment['risk_level'] in ['high', 'very_high']:
            assessment['recommendations'].append("Implement strict risk management controls.")
        
        return assessment
    
    def _save_validation_results(self, validation_results: Dict):
        """Save comprehensive validation results"""
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        strategy_name = validation_results.get('strategy_name', 'unknown')
        filename = self.results_dir / f"comprehensive_validation_{strategy_name}_{timestamp}.json"
        
        with open(filename, 'w') as f:
            json.dump(validation_results, f, indent=2, default=str)
        
        logger.info(f"💾 Validation results saved: {filename}")

def main():
    """Main validation function"""
    
    parser = argparse.ArgumentParser(description='Comprehensive Strategy Validator')
    parser.add_argument('--strategy', type=str, default='enhanced_grid', help='Strategy name')
    parser.add_argument('--start-date', type=str, default='2024-01-01', help='Start date (YYYY-MM-DD)')
    parser.add_argument('--end-date', type=str, default='2024-06-01', help='End date (YYYY-MM-DD)')
    parser.add_argument('--db-path', type=str, default='data/bitcoin_historical.db', help='Database path')
    
    args = parser.parse_args()
    
    try:
        # Initialize validator
        validator = ComprehensiveValidator(db_path=args.db_path)
        
        # Run comprehensive validation
        results = validator.run_comprehensive_validation(
            strategy_name=args.strategy,
            start_date=args.start_date,
            end_date=args.end_date
        )
        
        # Display summary
        assessment = results['overall_assessment']
        
        print("\n🔬 COMPREHENSIVE VALIDATION COMPLETE!")
        print("="*60)
        print(f"📊 Strategy: {args.strategy}")
        print(f"📅 Period: {args.start_date} to {args.end_date}")
        print(f"🎯 Validation Score: {assessment['validation_score']:.1f}/100")
        print(f"📝 Grade: {assessment['validation_grade']}")
        print(f"⚠️ Risk Level: {assessment['risk_level']}")
        print(f"✅ Implementation Ready: {assessment['implementation_ready']}")
        print("\n🔍 Key Findings:")
        for finding in assessment['key_findings']:
            print(f"  • {finding}")
        print("\n💡 Recommendations:")
        for rec in assessment['recommendations']:
            print(f"  • {rec}")
        print("="*60)
        
    except Exception as e:
        logger.error(f"❌ Validation failed: {e}")
        print(f"❌ Validation failed: {e}")

if __name__ == "__main__":
    main()
