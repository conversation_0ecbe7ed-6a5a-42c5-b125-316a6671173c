#!/usr/bin/env python3
"""
TRADING ENVIRONMENT - OPENAI GYM STYLE RL ENVIRONMENT
====================================================
OpenAI Gym-compatible trading environment for reinforcement learning.
Integrates with realistic trade simulator for proper training.
"""

import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
import sys
import os

# Add paths for imports
sys.path.append(os.path.join(os.path.dirname(__file__), '..', 'backtesting'))
from realistic_trade_simulator import RealTradeSimulator, TradeExecution

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class TradingEnvironment:
    """
    OpenAI Gym-style trading environment for RL training
    
    State Space: Market data + portfolio state + technical indicators
    Action Space: 0=Hold, 1=Buy, 2=Sell, 3=Adjust_Grid
    Reward: Risk-adjusted returns with drawdown penalties
    """
    
    def __init__(self, 
                 historical_data: pd.DataFrame,
                 initial_balance: float = 300.0,
                 lookback_window: int = 20,
                 max_episode_steps: int = 1000):
        
        self.historical_data = historical_data
        self.initial_balance = initial_balance
        self.lookback_window = lookback_window
        self.max_episode_steps = max_episode_steps
        
        # Environment state
        self.current_step = 0
        self.episode_start_step = 0
        self.done = False
        
        # Trading simulator
        self.simulator = RealTradeSimulator()
        self.reset_simulator()
        
        # State and action spaces
        self.observation_space_size = self._calculate_observation_space_size()
        self.action_space_size = 4  # Hold, Buy, Sell, Adjust_Grid
        
        # Performance tracking
        self.episode_trades = []
        self.episode_rewards = []
        self.portfolio_history = []
        
        # Risk management
        self.max_drawdown_threshold = 0.20  # 20% max drawdown
        self.min_balance_threshold = 200.0   # Minimum $200 balance
        
        logger.info(f"🎯 Trading Environment initialized")
        logger.info(f"📊 Data points: {len(self.historical_data)}")
        logger.info(f"🔢 Observation space: {self.observation_space_size}")
        logger.info(f"🎮 Action space: {self.action_space_size}")
    
    def _calculate_observation_space_size(self) -> int:
        """Calculate the size of observation space"""
        # Market data: OHLCV (5) + Technical indicators (10) + Portfolio state (5)
        market_features = 5  # open, high, low, close, volume
        technical_features = 10  # RSI, MACD, BB, etc.
        portfolio_features = 5  # balance, pnl, drawdown, active_orders, position
        
        return (market_features + technical_features + portfolio_features) * self.lookback_window
    
    def reset_simulator(self):
        """Reset the trading simulator"""
        self.simulator = RealTradeSimulator()
        self.simulator.portfolio_balance = self.initial_balance
    
    def reset(self) -> np.ndarray:
        """Reset environment for new episode"""

        # Random start position (but leave enough room for episode)
        max_start = len(self.historical_data) - self.max_episode_steps - self.lookback_window

        # Ensure we have enough data for at least one episode
        if max_start <= self.lookback_window:
            # Not enough data for random start, use fixed start
            self.episode_start_step = self.lookback_window
        else:
            self.episode_start_step = np.random.randint(self.lookback_window, max_start)

        self.current_step = self.episode_start_step
        self.done = False
        
        # Reset simulator
        self.reset_simulator()
        
        # Reset tracking
        self.episode_trades = []
        self.episode_rewards = []
        self.portfolio_history = []
        
        # Get initial observation
        observation = self._get_observation()
        
        logger.info(f"🔄 Environment reset - Episode starting at step {self.current_step}")
        return observation
    
    def _get_observation(self) -> np.ndarray:
        """Get current observation state"""
        
        # Get lookback window of market data
        start_idx = max(0, self.current_step - self.lookback_window)
        end_idx = self.current_step + 1
        
        market_window = self.historical_data.iloc[start_idx:end_idx]
        
        # Market features (normalized)
        market_features = []
        for _, row in market_window.iterrows():
            # Normalize prices relative to current close
            current_close = row['close']
            normalized_ohlc = [
                row['open'] / current_close,
                row['high'] / current_close,
                row['low'] / current_close,
                row['close'] / current_close,
                np.log(row['volume'] + 1) / 20  # Log-normalized volume
            ]
            market_features.extend(normalized_ohlc)
        
        # Technical indicators
        technical_features = self._calculate_technical_indicators(market_window)
        
        # Portfolio state
        portfolio_features = self._get_portfolio_state()
        
        # Combine all features
        observation = np.array(market_features + technical_features + portfolio_features, dtype=np.float32)
        
        # Pad if necessary (for first few steps)
        if len(observation) < self.observation_space_size:
            padding = np.zeros(self.observation_space_size - len(observation))
            observation = np.concatenate([padding, observation])
        
        return observation[:self.observation_space_size]
    
    def _calculate_technical_indicators(self, market_window: pd.DataFrame) -> List[float]:
        """Calculate technical indicators for observation"""
        
        if len(market_window) < 2:
            return [0.0] * (10 * self.lookback_window)
        
        indicators = []
        
        for _, row in market_window.iterrows():
            # Simple technical indicators (normalized)
            close = row['close']
            high = row['high']
            low = row['low']
            volume = row['volume']
            
            # Price-based indicators
            price_change = (close - market_window['close'].iloc[0]) / market_window['close'].iloc[0]
            volatility = (high - low) / close
            
            # Volume indicators
            volume_ratio = volume / (market_window['volume'].mean() + 1e-8)
            
            # Simple momentum
            if len(market_window) >= 5:
                momentum_5 = (close - market_window['close'].iloc[-5]) / market_window['close'].iloc[-5]
            else:
                momentum_5 = 0.0
            
            # Combine indicators
            row_indicators = [
                price_change,
                volatility,
                np.log(volume_ratio + 1),
                momentum_5,
                (close - market_window['close'].mean()) / market_window['close'].std() if market_window['close'].std() > 0 else 0,
                0.0,  # Placeholder for RSI
                0.0,  # Placeholder for MACD
                0.0,  # Placeholder for Bollinger Bands
                0.0,  # Placeholder for additional indicator
                0.0   # Placeholder for additional indicator
            ]
            
            indicators.extend(row_indicators)
        
        return indicators
    
    def _get_portfolio_state(self) -> List[float]:
        """Get current portfolio state"""
        
        stats = self.simulator.get_simulation_stats()
        
        # Normalize portfolio features
        balance_ratio = stats['portfolio_balance'] / self.initial_balance
        pnl_ratio = stats['pnl'] / self.initial_balance
        
        # Calculate current drawdown
        if self.portfolio_history:
            peak_balance = max(p['balance'] for p in self.portfolio_history)
            current_drawdown = (peak_balance - stats['portfolio_balance']) / peak_balance
        else:
            current_drawdown = 0.0
        
        # Active orders ratio
        active_orders_ratio = min(stats['active_orders'] / 20.0, 1.0)  # Normalize to max 20 orders
        
        # Position indicator (simplified)
        position_indicator = 0.5  # Neutral position
        
        portfolio_state = [
            balance_ratio,
            pnl_ratio,
            current_drawdown,
            active_orders_ratio,
            position_indicator
        ]
        
        # Repeat for lookback window
        return portfolio_state * self.lookback_window
    
    def step(self, action: int) -> Tuple[np.ndarray, float, bool, Dict]:
        """Execute one step in the environment"""
        
        if self.done:
            raise ValueError("Episode is done. Call reset() to start new episode.")
        
        # Get current market data
        current_market_data = self.historical_data.iloc[self.current_step]
        
        # Convert to dict format for simulator
        market_data_dict = {
            'datetime': current_market_data.get('datetime', datetime.now()),
            'open': current_market_data['open'],
            'high': current_market_data['high'],
            'low': current_market_data['low'],
            'close': current_market_data['close'],
            'volume': current_market_data['volume']
        }
        
        # Execute action
        executions = self._execute_action(action, market_data_dict)
        
        # Process market data through simulator
        simulator_executions = self.simulator.process_market_bar(market_data_dict)
        
        # Combine executions
        all_executions = executions + simulator_executions
        self.episode_trades.extend(all_executions)
        
        # Calculate reward
        reward = self._calculate_reward(action, all_executions, market_data_dict)
        self.episode_rewards.append(reward)
        
        # Update portfolio history
        stats = self.simulator.get_simulation_stats()
        self.portfolio_history.append({
            'step': self.current_step,
            'balance': stats['portfolio_balance'],
            'pnl': stats['pnl'],
            'price': current_market_data['close']
        })
        
        # Check if episode is done
        self.done = self._check_episode_done(stats)
        
        # Move to next step
        self.current_step += 1
        
        # Get next observation
        next_observation = self._get_observation() if not self.done else np.zeros(self.observation_space_size)
        
        # Create info dict
        info = {
            'portfolio_balance': stats['portfolio_balance'],
            'pnl': stats['pnl'],
            'total_trades': stats['total_trades'],
            'executions': len(all_executions),
            'current_price': current_market_data['close']
        }
        
        return next_observation, reward, self.done, info
    
    def _execute_action(self, action: int, market_data: Dict) -> List[TradeExecution]:
        """Execute the chosen action"""
        
        executions = []
        
        # Action space: 0=Hold, 1=Buy, 2=Sell, 3=Adjust_Grid
        if action == 0:
            # Hold - no additional action needed
            pass
        elif action == 1:
            # Buy - place additional buy order
            executions.extend(self._place_buy_order(market_data))
        elif action == 2:
            # Sell - place additional sell order
            executions.extend(self._place_sell_order(market_data))
        elif action == 3:
            # Adjust Grid - force grid update
            self.simulator.update_grid(market_data['close'], market_data['datetime'])
        
        return executions
    
    def _place_buy_order(self, market_data: Dict) -> List[TradeExecution]:
        """Place additional buy order"""
        # For now, return empty list - grid system handles orders
        # This could be enhanced to place market orders
        return []
    
    def _place_sell_order(self, market_data: Dict) -> List[TradeExecution]:
        """Place additional sell order"""
        # For now, return empty list - grid system handles orders
        # This could be enhanced to place market orders
        return []
    
    def _calculate_reward(self, action: int, executions: List[TradeExecution], market_data: Dict) -> float:
        """Calculate reward for the current step"""
        
        stats = self.simulator.get_simulation_stats()
        
        # Base reward: portfolio change
        if self.portfolio_history:
            prev_balance = self.portfolio_history[-1]['balance']
            balance_change = stats['portfolio_balance'] - prev_balance
            base_reward = balance_change / self.initial_balance  # Normalized
        else:
            base_reward = 0.0
        
        # Execution reward: successful trades
        execution_reward = len(executions) * 0.01  # Small bonus for activity
        
        # Risk penalty: drawdown
        if self.portfolio_history:
            peak_balance = max(p['balance'] for p in self.portfolio_history)
            current_drawdown = (peak_balance - stats['portfolio_balance']) / peak_balance
            drawdown_penalty = -current_drawdown * 2.0  # Penalty for drawdown
        else:
            drawdown_penalty = 0.0
        
        # Commission penalty
        commission_penalty = -sum(ex.commission for ex in executions) / self.initial_balance
        
        # Total reward
        total_reward = base_reward + execution_reward + drawdown_penalty + commission_penalty
        
        return total_reward
    
    def _check_episode_done(self, stats: Dict) -> bool:
        """Check if episode should end"""
        
        # Episode length limit
        if self.current_step >= self.episode_start_step + self.max_episode_steps:
            return True
        
        # Data limit
        if self.current_step >= len(self.historical_data) - 1:
            return True
        
        # Risk management: max drawdown
        if self.portfolio_history:
            peak_balance = max(p['balance'] for p in self.portfolio_history)
            current_drawdown = (peak_balance - stats['portfolio_balance']) / peak_balance
            if current_drawdown > self.max_drawdown_threshold:
                logger.warning(f"⚠️ Episode ended due to max drawdown: {current_drawdown:.2%}")
                return True
        
        # Risk management: minimum balance
        if stats['portfolio_balance'] < self.min_balance_threshold:
            logger.warning(f"⚠️ Episode ended due to low balance: ${stats['portfolio_balance']:.2f}")
            return True
        
        return False
    
    def get_episode_summary(self) -> Dict:
        """Get summary of current episode"""
        
        if not self.portfolio_history:
            return {}
        
        stats = self.simulator.get_simulation_stats()
        
        # Calculate episode metrics
        initial_balance = self.portfolio_history[0]['balance']
        final_balance = stats['portfolio_balance']
        total_return = (final_balance - initial_balance) / initial_balance
        
        # Calculate Sharpe ratio (simplified)
        if len(self.episode_rewards) > 1:
            avg_reward = np.mean(self.episode_rewards)
            reward_std = np.std(self.episode_rewards)
            sharpe_ratio = avg_reward / reward_std if reward_std > 0 else 0.0
        else:
            sharpe_ratio = 0.0
        
        # Calculate max drawdown
        peak_balance = max(p['balance'] for p in self.portfolio_history)
        max_drawdown = (peak_balance - min(p['balance'] for p in self.portfolio_history)) / peak_balance
        
        return {
            'episode_length': len(self.portfolio_history),
            'total_trades': len(self.episode_trades),
            'initial_balance': initial_balance,
            'final_balance': final_balance,
            'total_return': total_return,
            'total_return_pct': total_return * 100,
            'max_drawdown': max_drawdown,
            'max_drawdown_pct': max_drawdown * 100,
            'sharpe_ratio': sharpe_ratio,
            'total_reward': sum(self.episode_rewards),
            'avg_reward': np.mean(self.episode_rewards) if self.episode_rewards else 0.0
        }

def main():
    """Test the trading environment"""
    
    # Create sample data
    dates = pd.date_range(start='2024-01-01', periods=1000, freq='1min')
    sample_data = pd.DataFrame({
        'datetime': dates,
        'open': 50000 + np.random.randn(1000) * 100,
        'high': 50000 + np.random.randn(1000) * 100 + 50,
        'low': 50000 + np.random.randn(1000) * 100 - 50,
        'close': 50000 + np.random.randn(1000) * 100,
        'volume': 1000000 + np.random.randn(1000) * 100000
    })
    
    # Ensure high >= low and proper OHLC relationships
    sample_data['high'] = np.maximum(sample_data[['open', 'close']].max(axis=1), sample_data['high'])
    sample_data['low'] = np.minimum(sample_data[['open', 'close']].min(axis=1), sample_data['low'])
    
    # Test environment
    env = TradingEnvironment(sample_data)
    
    print("🧪 Testing Trading Environment...")
    
    # Test reset
    obs = env.reset()
    print(f"✅ Environment reset - Observation shape: {obs.shape}")
    
    # Test steps
    for i in range(10):
        action = np.random.randint(0, 4)  # Random action
        obs, reward, done, info = env.step(action)
        
        print(f"Step {i+1}: Action={action}, Reward={reward:.4f}, Balance=${info['portfolio_balance']:.2f}")
        
        if done:
            print("Episode ended")
            break
    
    # Get episode summary
    summary = env.get_episode_summary()
    print(f"\n📊 Episode Summary:")
    for key, value in summary.items():
        print(f"  {key}: {value}")

if __name__ == "__main__":
    main()
