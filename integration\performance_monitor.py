#!/usr/bin/env python3
"""
PERFORMANCE MONITOR - REAL-TIME MONITORING SYSTEM
================================================
Real-time monitoring of trading system performance with:
- Live performance tracking
- Risk monitoring and alerts
- Dashboard generation
- Automated reporting
"""

import numpy as np
import pandas as pd
import json
import time
import threading
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple, Any
import logging
import sys
import os
from pathlib import Path
import sqlite3
from dataclasses import dataclass
from enum import Enum

# Setup logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

class AlertLevel(Enum):
    INFO = "info"
    WARNING = "warning"
    CRITICAL = "critical"

@dataclass
class Alert:
    """Alert data structure"""
    id: str
    level: AlertLevel
    message: str
    timestamp: datetime
    acknowledged: bool = False
    resolved: bool = False

@dataclass
class PerformanceSnapshot:
    """Performance snapshot data structure"""
    timestamp: datetime
    balance: float
    equity: float
    total_return_pct: float
    daily_return_pct: float
    sharpe_ratio: float
    max_drawdown_pct: float
    win_rate: float
    total_trades: int
    active_positions: int
    risk_score: float

class PerformanceMonitor:
    """
    Real-time performance monitoring system
    """
    
    def __init__(self, 
                 db_path: str = "data/bitcoin_historical.db",
                 monitoring_frequency: int = 60):  # seconds
        
        self.db_path = db_path
        self.monitoring_frequency = monitoring_frequency
        
        # Monitoring configuration
        self.config = {
            'risk_thresholds': {
                'max_drawdown_pct': 20.0,
                'min_sharpe_ratio': 0.3,
                'max_daily_loss_pct': 5.0,
                'max_consecutive_losses': 5,
                'min_win_rate': 0.4,
                'max_risk_score': 80.0
            },
            'alert_settings': {
                'email_enabled': False,
                'slack_enabled': False,
                'log_all_alerts': True,
                'alert_cooldown_minutes': 30
            },
            'reporting': {
                'daily_report': True,
                'weekly_report': True,
                'monthly_report': True,
                'real_time_dashboard': True
            }
        }
        
        # Monitoring state
        self.running = False
        self.monitor_thread = None
        self.last_alert_times: Dict[str, datetime] = {}
        
        # Data storage
        self.performance_history: List[PerformanceSnapshot] = []
        self.alerts: List[Alert] = []
        self.daily_summaries: List[Dict] = []
        
        # Results directories
        self.results_dir = Path("results/monitoring")
        self.alerts_dir = Path("results/monitoring/alerts")
        self.reports_dir = Path("results/monitoring/reports")
        
        # Create directories
        for directory in [self.results_dir, self.alerts_dir, self.reports_dir]:
            directory.mkdir(parents=True, exist_ok=True)
        
        logger.info(f"📊 Performance Monitor initialized")
        logger.info(f"⏱️ Monitoring frequency: {monitoring_frequency} seconds")
        logger.info(f"⚠️ Risk thresholds configured")
    
    def start_monitoring(self):
        """Start performance monitoring"""
        
        logger.info(f"🚀 Starting performance monitoring")
        
        try:
            self.running = True
            
            # Start monitoring thread
            self.monitor_thread = threading.Thread(target=self._run_monitoring_loop, daemon=True)
            self.monitor_thread.start()
            
            logger.info(f"✅ Performance monitoring started")
            
        except Exception as e:
            logger.error(f"❌ Failed to start performance monitoring: {e}")
            raise
    
    def stop_monitoring(self):
        """Stop performance monitoring"""
        
        logger.info(f"🛑 Stopping performance monitoring")
        
        try:
            self.running = False
            
            # Wait for monitoring thread to finish
            if self.monitor_thread and self.monitor_thread.is_alive():
                self.monitor_thread.join(timeout=10)
            
            # Generate final report
            self._generate_final_report()
            
            logger.info(f"✅ Performance monitoring stopped")
            
        except Exception as e:
            logger.error(f"❌ Error stopping performance monitoring: {e}")
    
    def _run_monitoring_loop(self):
        """Main monitoring loop"""
        
        logger.info(f"📊 Performance monitoring loop started")
        
        while self.running:
            try:
                # Collect performance data
                snapshot = self._collect_performance_snapshot()
                
                if snapshot:
                    # Store snapshot
                    self.performance_history.append(snapshot)
                    
                    # Check for alerts
                    self._check_risk_thresholds(snapshot)
                    
                    # Update dashboard
                    self._update_dashboard(snapshot)
                    
                    # Cleanup old data
                    self._cleanup_old_data()
                
                time.sleep(self.monitoring_frequency)
                
            except Exception as e:
                logger.error(f"❌ Monitoring loop error: {e}")
                time.sleep(self.monitoring_frequency)
        
        logger.info(f"📊 Performance monitoring loop stopped")
    
    def _collect_performance_snapshot(self) -> Optional[PerformanceSnapshot]:
        """Collect current performance snapshot"""
        
        try:
            # This would integrate with the paper trading engine
            # For now, simulate performance data
            
            current_time = datetime.now()
            
            # Simulate performance metrics
            base_balance = 300.0
            time_factor = (current_time.hour * 60 + current_time.minute) / (24 * 60)
            
            # Simulate some performance variation
            balance = base_balance + np.random.normal(0, 10)
            equity = balance + np.random.normal(0, 5)
            total_return_pct = ((equity - base_balance) / base_balance) * 100
            
            # Calculate daily return
            if len(self.performance_history) > 0:
                yesterday_equity = self.performance_history[-1].equity
                daily_return_pct = ((equity - yesterday_equity) / yesterday_equity) * 100
            else:
                daily_return_pct = 0.0
            
            # Simulate other metrics
            sharpe_ratio = np.random.normal(1.0, 0.3)
            max_drawdown_pct = abs(np.random.normal(5.0, 2.0))
            win_rate = np.random.uniform(0.45, 0.65)
            total_trades = len(self.performance_history) + np.random.randint(0, 3)
            active_positions = np.random.randint(0, 5)
            
            # Calculate risk score
            risk_score = self._calculate_risk_score(
                max_drawdown_pct, sharpe_ratio, win_rate, daily_return_pct
            )
            
            snapshot = PerformanceSnapshot(
                timestamp=current_time,
                balance=balance,
                equity=equity,
                total_return_pct=total_return_pct,
                daily_return_pct=daily_return_pct,
                sharpe_ratio=sharpe_ratio,
                max_drawdown_pct=max_drawdown_pct,
                win_rate=win_rate,
                total_trades=total_trades,
                active_positions=active_positions,
                risk_score=risk_score
            )
            
            return snapshot
            
        except Exception as e:
            logger.error(f"❌ Error collecting performance snapshot: {e}")
            return None
    
    def _calculate_risk_score(self, max_drawdown_pct: float, sharpe_ratio: float, 
                             win_rate: float, daily_return_pct: float) -> float:
        """Calculate overall risk score (0-100, higher = riskier)"""
        
        risk_score = 0
        
        # Drawdown component (0-40 points)
        if max_drawdown_pct > 20:
            risk_score += 40
        elif max_drawdown_pct > 15:
            risk_score += 30
        elif max_drawdown_pct > 10:
            risk_score += 20
        elif max_drawdown_pct > 5:
            risk_score += 10
        
        # Sharpe ratio component (0-25 points, inverted)
        if sharpe_ratio < 0:
            risk_score += 25
        elif sharpe_ratio < 0.5:
            risk_score += 20
        elif sharpe_ratio < 1.0:
            risk_score += 10
        elif sharpe_ratio < 1.5:
            risk_score += 5
        
        # Win rate component (0-20 points, inverted)
        if win_rate < 0.3:
            risk_score += 20
        elif win_rate < 0.4:
            risk_score += 15
        elif win_rate < 0.5:
            risk_score += 10
        elif win_rate < 0.6:
            risk_score += 5
        
        # Daily return volatility (0-15 points)
        if abs(daily_return_pct) > 10:
            risk_score += 15
        elif abs(daily_return_pct) > 5:
            risk_score += 10
        elif abs(daily_return_pct) > 2:
            risk_score += 5
        
        return min(risk_score, 100)
    
    def _check_risk_thresholds(self, snapshot: PerformanceSnapshot):
        """Check risk thresholds and generate alerts"""
        
        thresholds = self.config['risk_thresholds']
        
        # Check max drawdown
        if snapshot.max_drawdown_pct > thresholds['max_drawdown_pct']:
            self._create_alert(
                AlertLevel.CRITICAL,
                f"Maximum drawdown exceeded: {snapshot.max_drawdown_pct:.2f}% > {thresholds['max_drawdown_pct']:.2f}%",
                "max_drawdown"
            )
        
        # Check Sharpe ratio
        if snapshot.sharpe_ratio < thresholds['min_sharpe_ratio']:
            self._create_alert(
                AlertLevel.WARNING,
                f"Sharpe ratio below threshold: {snapshot.sharpe_ratio:.2f} < {thresholds['min_sharpe_ratio']:.2f}",
                "sharpe_ratio"
            )
        
        # Check daily loss
        if snapshot.daily_return_pct < -thresholds['max_daily_loss_pct']:
            self._create_alert(
                AlertLevel.CRITICAL,
                f"Daily loss exceeded: {snapshot.daily_return_pct:.2f}% < -{thresholds['max_daily_loss_pct']:.2f}%",
                "daily_loss"
            )
        
        # Check win rate
        if snapshot.win_rate < thresholds['min_win_rate']:
            self._create_alert(
                AlertLevel.WARNING,
                f"Win rate below threshold: {snapshot.win_rate:.2%} < {thresholds['min_win_rate']:.2%}",
                "win_rate"
            )
        
        # Check overall risk score
        if snapshot.risk_score > thresholds['max_risk_score']:
            self._create_alert(
                AlertLevel.WARNING,
                f"Risk score elevated: {snapshot.risk_score:.1f} > {thresholds['max_risk_score']:.1f}",
                "risk_score"
            )
    
    def _create_alert(self, level: AlertLevel, message: str, alert_type: str):
        """Create and process an alert"""
        
        # Check cooldown period
        cooldown_minutes = self.config['alert_settings']['alert_cooldown_minutes']
        last_alert_time = self.last_alert_times.get(alert_type)
        
        if last_alert_time:
            time_since_last = datetime.now() - last_alert_time
            if time_since_last.total_seconds() < cooldown_minutes * 60:
                return  # Skip alert due to cooldown
        
        # Create alert
        alert_id = f"alert_{datetime.now().strftime('%Y%m%d_%H%M%S')}_{alert_type}"
        alert = Alert(
            id=alert_id,
            level=level,
            message=message,
            timestamp=datetime.now()
        )
        
        # Store alert
        self.alerts.append(alert)
        self.last_alert_times[alert_type] = datetime.now()
        
        # Log alert
        if level == AlertLevel.CRITICAL:
            logger.critical(f"🚨 CRITICAL ALERT: {message}")
        elif level == AlertLevel.WARNING:
            logger.warning(f"⚠️ WARNING ALERT: {message}")
        else:
            logger.info(f"ℹ️ INFO ALERT: {message}")
        
        # Save alert to file
        self._save_alert(alert)
        
        # Send notifications (if configured)
        self._send_alert_notifications(alert)
    
    def _save_alert(self, alert: Alert):
        """Save alert to file"""
        
        try:
            alert_data = {
                'id': alert.id,
                'level': alert.level.value,
                'message': alert.message,
                'timestamp': alert.timestamp.isoformat(),
                'acknowledged': alert.acknowledged,
                'resolved': alert.resolved
            }
            
            alert_file = self.alerts_dir / f"{alert.id}.json"
            with open(alert_file, 'w') as f:
                json.dump(alert_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"❌ Error saving alert: {e}")
    
    def _send_alert_notifications(self, alert: Alert):
        """Send alert notifications"""
        
        # Email notifications (if configured)
        if self.config['alert_settings']['email_enabled']:
            self._send_email_alert(alert)
        
        # Slack notifications (if configured)
        if self.config['alert_settings']['slack_enabled']:
            self._send_slack_alert(alert)
    
    def _send_email_alert(self, alert: Alert):
        """Send email alert (placeholder)"""
        logger.info(f"📧 Email alert would be sent: {alert.message}")
    
    def _send_slack_alert(self, alert: Alert):
        """Send Slack alert (placeholder)"""
        logger.info(f"💬 Slack alert would be sent: {alert.message}")
    
    def _update_dashboard(self, snapshot: PerformanceSnapshot):
        """Update real-time dashboard"""
        
        try:
            dashboard_data = {
                'last_update': snapshot.timestamp.isoformat(),
                'current_performance': {
                    'balance': snapshot.balance,
                    'equity': snapshot.equity,
                    'total_return_pct': snapshot.total_return_pct,
                    'daily_return_pct': snapshot.daily_return_pct,
                    'sharpe_ratio': snapshot.sharpe_ratio,
                    'max_drawdown_pct': snapshot.max_drawdown_pct,
                    'win_rate': snapshot.win_rate,
                    'total_trades': snapshot.total_trades,
                    'active_positions': snapshot.active_positions,
                    'risk_score': snapshot.risk_score
                },
                'recent_alerts': [
                    {
                        'level': alert.level.value,
                        'message': alert.message,
                        'timestamp': alert.timestamp.isoformat()
                    } for alert in self.alerts[-5:]  # Last 5 alerts
                ],
                'performance_trend': self._calculate_performance_trend()
            }
            
            dashboard_file = self.results_dir / "dashboard.json"
            with open(dashboard_file, 'w') as f:
                json.dump(dashboard_data, f, indent=2)
                
        except Exception as e:
            logger.error(f"❌ Error updating dashboard: {e}")
    
    def _calculate_performance_trend(self) -> Dict:
        """Calculate performance trend indicators"""
        
        if len(self.performance_history) < 10:
            return {'trend': 'insufficient_data'}
        
        # Get recent performance data
        recent_snapshots = self.performance_history[-10:]
        returns = [s.daily_return_pct for s in recent_snapshots]
        
        # Calculate trend
        avg_return = np.mean(returns)
        return_volatility = np.std(returns)
        
        if avg_return > 0.5:
            trend = 'positive'
        elif avg_return < -0.5:
            trend = 'negative'
        else:
            trend = 'neutral'
        
        return {
            'trend': trend,
            'avg_daily_return': avg_return,
            'volatility': return_volatility,
            'consistency': 1 / (1 + return_volatility) if return_volatility > 0 else 1.0
        }
    
    def _cleanup_old_data(self):
        """Cleanup old monitoring data"""
        
        # Keep only last 1000 snapshots
        if len(self.performance_history) > 1000:
            self.performance_history = self.performance_history[-1000:]
        
        # Keep only last 100 alerts
        if len(self.alerts) > 100:
            self.alerts = self.alerts[-100:]
    
    def _generate_final_report(self):
        """Generate final monitoring report"""
        
        try:
            if not self.performance_history:
                logger.warning("⚠️ No performance data to report")
                return
            
            # Calculate summary statistics
            total_snapshots = len(self.performance_history)
            first_snapshot = self.performance_history[0]
            last_snapshot = self.performance_history[-1]
            
            monitoring_duration = last_snapshot.timestamp - first_snapshot.timestamp
            
            # Performance statistics
            returns = [s.daily_return_pct for s in self.performance_history]
            avg_daily_return = np.mean(returns)
            return_volatility = np.std(returns)
            max_daily_return = max(returns)
            min_daily_return = min(returns)
            
            # Risk statistics
            risk_scores = [s.risk_score for s in self.performance_history]
            avg_risk_score = np.mean(risk_scores)
            max_risk_score = max(risk_scores)
            
            # Alert statistics
            alert_counts = {}
            for alert in self.alerts:
                level = alert.level.value
                alert_counts[level] = alert_counts.get(level, 0) + 1
            
            final_report = {
                'monitoring_summary': {
                    'start_time': first_snapshot.timestamp.isoformat(),
                    'end_time': last_snapshot.timestamp.isoformat(),
                    'duration_hours': monitoring_duration.total_seconds() / 3600,
                    'total_snapshots': total_snapshots,
                    'monitoring_frequency_seconds': self.monitoring_frequency
                },
                'performance_summary': {
                    'initial_equity': first_snapshot.equity,
                    'final_equity': last_snapshot.equity,
                    'total_return_pct': last_snapshot.total_return_pct,
                    'avg_daily_return_pct': avg_daily_return,
                    'return_volatility_pct': return_volatility,
                    'max_daily_return_pct': max_daily_return,
                    'min_daily_return_pct': min_daily_return,
                    'final_sharpe_ratio': last_snapshot.sharpe_ratio,
                    'max_drawdown_pct': last_snapshot.max_drawdown_pct,
                    'final_win_rate': last_snapshot.win_rate,
                    'total_trades': last_snapshot.total_trades
                },
                'risk_summary': {
                    'avg_risk_score': avg_risk_score,
                    'max_risk_score': max_risk_score,
                    'final_risk_score': last_snapshot.risk_score,
                    'risk_threshold_breaches': len(self.alerts)
                },
                'alert_summary': {
                    'total_alerts': len(self.alerts),
                    'alert_breakdown': alert_counts,
                    'alerts_per_hour': len(self.alerts) / max(monitoring_duration.total_seconds() / 3600, 1)
                },
                'configuration': self.config
            }
            
            # Save final report
            report_file = self.reports_dir / f"final_monitoring_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
            with open(report_file, 'w') as f:
                json.dump(final_report, f, indent=2)
            
            logger.info(f"📊 Final monitoring report generated: {report_file}")
            
        except Exception as e:
            logger.error(f"❌ Error generating final report: {e}")
    
    def get_current_status(self) -> Dict:
        """Get current monitoring status"""
        
        if not self.performance_history:
            return {'status': 'no_data'}
        
        latest_snapshot = self.performance_history[-1]
        recent_alerts = [alert for alert in self.alerts if 
                        (datetime.now() - alert.timestamp).total_seconds() < 3600]  # Last hour
        
        return {
            'status': 'active' if self.running else 'stopped',
            'last_update': latest_snapshot.timestamp.isoformat(),
            'current_performance': {
                'equity': latest_snapshot.equity,
                'total_return_pct': latest_snapshot.total_return_pct,
                'daily_return_pct': latest_snapshot.daily_return_pct,
                'risk_score': latest_snapshot.risk_score
            },
            'recent_alerts': len(recent_alerts),
            'monitoring_duration_hours': (datetime.now() - self.performance_history[0].timestamp).total_seconds() / 3600
        }
    
    def acknowledge_alert(self, alert_id: str) -> bool:
        """Acknowledge an alert"""
        
        for alert in self.alerts:
            if alert.id == alert_id:
                alert.acknowledged = True
                logger.info(f"✅ Alert acknowledged: {alert_id}")
                return True
        
        logger.warning(f"⚠️ Alert not found: {alert_id}")
        return False

def main():
    """Test performance monitor"""
    
    print("🧪 Testing Performance Monitor...")
    
    try:
        # Create monitor
        monitor = PerformanceMonitor(monitoring_frequency=5)  # 5 second intervals for testing
        
        print(f"✅ Performance monitor created")
        print(f"📊 Monitoring frequency: {monitor.monitoring_frequency} seconds")
        
        # Start monitoring
        monitor.start_monitoring()
        print(f"🚀 Monitoring started")
        
        # Let it run for a bit
        time.sleep(15)
        
        # Check status
        status = monitor.get_current_status()
        print(f"📊 Current status: {status['status']}")
        if 'current_performance' in status:
            perf = status['current_performance']
            print(f"  Equity: ${perf['equity']:.2f}")
            print(f"  Total return: {perf['total_return_pct']:.2f}%")
            print(f"  Risk score: {perf['risk_score']:.1f}")
        
        # Stop monitoring
        monitor.stop_monitoring()
        print(f"🛑 Monitoring stopped")
        
        print("🎉 Performance monitor test completed!")
        
    except Exception as e:
        print(f"❌ Performance monitor test failed: {e}")

if __name__ == "__main__":
    main()
