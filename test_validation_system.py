#!/usr/bin/env python3
"""
TEST VALIDATION SYSTEM - PHASE 3 VALIDATION
==========================================
Tests the complete validation system including walk-forward analysis,
statistical testing, risk analysis, and performance comparison.
"""

import sys
import os
import numpy as np
import pandas as pd
from datetime import datetime, timedelta

# Add paths for imports
sys.path.append('validation')
sys.path.append('backtesting')

def test_statistical_validator():
    """Test statistical significance validator"""
    print("🧪 Testing Statistical Validator...")
    
    try:
        from statistical_validator import StatisticalValidator
        
        # Create sample data with known improvement
        np.random.seed(42)
        baseline_returns = np.random.normal(0.5, 2.0, 100)  # 0.5% mean return
        strategy_returns = baseline_returns + np.random.normal(0.3, 0.5, 100)  # Small improvement
        
        validator = StatisticalValidator()
        results = validator.test_strategy_significance(baseline_returns.tolist(), strategy_returns.tolist())
        
        print(f"✅ Statistical validation completed")
        print(f"  Sample size: {results['sample_size']}")
        print(f"  Conclusion: {results['overall_assessment']['conclusion']}")
        print(f"  Effect size: {results['effect_size']['effect_size_interpretation']}")
        print(f"  Significant tests: {len(results['overall_assessment']['significant_tests'])}")
        
        return True
        
    except Exception as e:
        print(f"❌ Statistical validator test failed: {e}")
        return False

def test_risk_analyzer():
    """Test risk analyzer"""
    print("\n🧪 Testing Risk Analyzer...")
    
    try:
        from risk_analyzer import RiskAnalyzer
        
        # Create sample return data with some volatility
        np.random.seed(42)
        returns = np.random.normal(0.1, 2.0, 252)  # Daily returns for one year
        
        # Add some extreme events
        returns[50] = -8.0  # Market crash
        returns[100] = 6.0  # Large positive move
        
        analyzer = RiskAnalyzer()
        results = analyzer.analyze_strategy_risk(returns.tolist())
        
        print(f"✅ Risk analysis completed")
        print(f"  Risk level: {results['overall_assessment']['risk_level']}")
        print(f"  Risk score: {results['overall_assessment']['risk_score']}/100")
        print(f"  Max drawdown: {results['drawdown_analysis']['max_drawdown_pct']:.2f}%")
        print(f"  Sharpe ratio: {results['risk_adjusted_performance']['sharpe_ratio']['annualized']:.2f}")
        print(f"  VaR 95%: {results['var_analysis']['confidence_95']['historical_var']:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Risk analyzer test failed: {e}")
        return False

def test_performance_comparator():
    """Test performance comparator"""
    print("\n🧪 Testing Performance Comparator...")
    
    try:
        from performance_comparator import PerformanceComparator
        
        # Create sample strategy and benchmark results
        strategy_results = {
            'strategy_name': 'test_strategy',
            'performance_summary': {
                'total_return_pct': 15.5,
                'sharpe_ratio': 1.2,
                'max_drawdown_pct': 8.5,
                'win_rate': 0.65,
                'total_trades': 50,
                'returns_list': np.random.normal(0.1, 1.0, 100).tolist()
            }
        }
        
        benchmark_results = {
            'buy_and_hold': {
                'performance_summary': {
                    'total_return_pct': 12.0,
                    'sharpe_ratio': 0.8,
                    'max_drawdown_pct': 15.0,
                    'win_rate': 0.55,
                    'total_trades': 1,
                    'returns_list': np.random.normal(0.08, 1.2, 100).tolist()
                }
            },
            'simple_grid': {
                'performance_summary': {
                    'total_return_pct': 10.5,
                    'sharpe_ratio': 0.9,
                    'max_drawdown_pct': 12.0,
                    'win_rate': 0.60,
                    'total_trades': 30,
                    'returns_list': np.random.normal(0.07, 1.1, 100).tolist()
                }
            }
        }
        
        comparator = PerformanceComparator()
        results = comparator.compare_strategies(strategy_results, benchmark_results)
        
        print(f"✅ Performance comparison completed")
        print(f"  Performance class: {results['overall_assessment']['performance_class']}")
        print(f"  Benchmarks outperformed: {results['overall_assessment']['benchmarks_outperformed']}/{results['overall_assessment']['total_benchmarks']}")
        print(f"  Overall percentile: {results['overall_assessment']['overall_percentile']:.1f}%")
        print(f"  Recommendation: {results['overall_assessment']['recommendation'][:50]}...")
        
        return True
        
    except Exception as e:
        print(f"❌ Performance comparator test failed: {e}")
        return False

def test_walk_forward_analyzer():
    """Test walk-forward analyzer"""
    print("\n🧪 Testing Walk-Forward Analyzer...")
    
    try:
        from walk_forward_analyzer import WalkForwardAnalyzer
        
        # Test period generation
        analyzer = WalkForwardAnalyzer()
        
        start_date = "2024-01-01"
        end_date = "2024-06-01"
        
        periods = analyzer.generate_walk_forward_periods(start_date, end_date)
        
        print(f"✅ Walk-forward analyzer tested")
        print(f"  Generated periods: {len(periods)}")
        if periods:
            print(f"  First period: {periods[0]['training_start']} to {periods[0]['validation_end']}")
            print(f"  Last period: {periods[-1]['training_start']} to {periods[-1]['validation_end']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Walk-forward analyzer test failed: {e}")
        return False

def test_comprehensive_validator():
    """Test comprehensive validator (without database)"""
    print("\n🧪 Testing Comprehensive Validator...")
    
    try:
        from comprehensive_validator import ComprehensiveValidator
        
        # Test initialization
        validator = ComprehensiveValidator()
        
        print(f"✅ Comprehensive validator initialized")
        print(f"  Validation types: {len(validator.config['validation_types'])}")
        print(f"  Benchmark strategies: {len(validator.config['benchmark_strategies'])}")
        
        # Test benchmark generation (simplified)
        sample_data = pd.DataFrame({
            'datetime': pd.date_range(start='2024-01-01', periods=100, freq='1H'),
            'open': 50000 + np.random.randn(100) * 100,
            'high': 50000 + np.random.randn(100) * 100 + 50,
            'low': 50000 + np.random.randn(100) * 100 - 50,
            'close': 50000 + np.random.randn(100) * 100,
            'volume': 1000000 + np.random.randn(100) * 100000
        })
        
        # Ensure proper OHLC relationships
        sample_data['high'] = np.maximum(sample_data[['open', 'close']].max(axis=1), sample_data['high'])
        sample_data['low'] = np.minimum(sample_data[['open', 'close']].min(axis=1), sample_data['low'])
        
        # Test buy and hold benchmark
        bnh_results = validator._run_buy_and_hold(sample_data)
        print(f"  Buy and hold return: {bnh_results['total_return_pct']:.2f}%")
        
        # Test random trading benchmark
        random_results = validator._run_random_trading(sample_data)
        print(f"  Random trading return: {random_results['total_return_pct']:.2f}%")
        
        return True
        
    except Exception as e:
        print(f"❌ Comprehensive validator test failed: {e}")
        return False

def test_integration():
    """Test integration between validation components"""
    print("\n🧪 Testing Validation System Integration...")
    
    try:
        # Test that all components can work together
        from statistical_validator import StatisticalValidator
        from risk_analyzer import RiskAnalyzer
        from performance_comparator import PerformanceComparator
        
        # Create consistent test data
        np.random.seed(42)
        strategy_returns = np.random.normal(0.12, 1.5, 100)
        baseline_returns = np.random.normal(0.08, 1.8, 100)
        
        # Test statistical validation
        stat_validator = StatisticalValidator()
        stat_results = stat_validator.test_strategy_significance(baseline_returns.tolist(), strategy_returns.tolist())
        
        # Test risk analysis
        risk_analyzer = RiskAnalyzer()
        risk_results = risk_analyzer.analyze_strategy_risk(strategy_returns.tolist())
        
        # Test performance comparison
        strategy_data = {
            'performance_summary': {
                'total_return_pct': 12.0,
                'sharpe_ratio': 1.1,
                'max_drawdown_pct': 10.0,
                'returns_list': strategy_returns.tolist()
            }
        }
        
        benchmark_data = {
            'baseline': {
                'performance_summary': {
                    'total_return_pct': 8.0,
                    'sharpe_ratio': 0.7,
                    'max_drawdown_pct': 15.0,
                    'returns_list': baseline_returns.tolist()
                }
            }
        }
        
        comparator = PerformanceComparator()
        comp_results = comparator.compare_strategies(strategy_data, benchmark_data)
        
        print(f"✅ Integration test completed")
        print(f"  Statistical conclusion: {stat_results['overall_assessment']['conclusion']}")
        print(f"  Risk level: {risk_results['overall_assessment']['risk_level']}")
        print(f"  Performance class: {comp_results['overall_assessment']['performance_class']}")
        
        return True
        
    except Exception as e:
        print(f"❌ Integration test failed: {e}")
        return False

def run_comprehensive_test():
    """Run comprehensive validation system test"""
    print("🔬 VALIDATION SYSTEM COMPREHENSIVE TEST")
    print("="*60)
    
    test_results = {
        'Statistical Validator': test_statistical_validator(),
        'Risk Analyzer': test_risk_analyzer(),
        'Performance Comparator': test_performance_comparator(),
        'Walk-Forward Analyzer': test_walk_forward_analyzer(),
        'Comprehensive Validator': test_comprehensive_validator(),
        'System Integration': test_integration()
    }
    
    print("\n📊 TEST RESULTS:")
    print("="*60)
    
    passed_tests = 0
    total_tests = len(test_results)
    
    for test_name, result in test_results.items():
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
        if result:
            passed_tests += 1
    
    success_rate = passed_tests / total_tests
    print(f"\n🎯 Overall Success Rate: {success_rate:.1%} ({passed_tests}/{total_tests})")
    
    if success_rate >= 0.8:
        print("\n🎉 VALIDATION SYSTEM IS READY!")
        print("✅ Statistical significance testing operational")
        print("✅ Risk analysis comprehensive")
        print("✅ Performance comparison functional")
        print("✅ Walk-forward analysis ready")
        print("✅ Comprehensive validation integrated")
        print("✅ System integration successful")
        print("\n🚀 Ready for Phase 3 completion!")
    else:
        print("\n⚠️ Some tests failed - please review and fix issues")
    
    return success_rate >= 0.8

def main():
    """Main test function"""
    try:
        success = run_comprehensive_test()
        
        if success:
            print("\n" + "="*70)
            print("🔬 PHASE 3 IMPLEMENTATION COMPLETE!")
            print("="*70)
            print("✅ Walk-forward analysis system ready")
            print("✅ Statistical significance testing implemented")
            print("✅ Comprehensive risk analysis operational")
            print("✅ Performance comparison system functional")
            print("✅ Integrated validation suite complete")
            print("\n🚀 NEXT STEPS:")
            print("1. Run: python validation/comprehensive_validator.py --strategy enhanced_grid")
            print("2. Monitor validation results in results/validation/ directory")
            print("3. Proceed to Phase 4: Integration & Testing")
            print("="*70)
        
        return success
        
    except Exception as e:
        print(f"❌ Test suite failed: {e}")
        return False

if __name__ == "__main__":
    main()
